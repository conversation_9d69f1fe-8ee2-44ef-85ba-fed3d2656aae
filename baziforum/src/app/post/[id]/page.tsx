import Link from 'next/link';
import { notFound } from 'next/navigation';
import { samplePosts } from '@/data/posts';
import { sampleComments } from '@/data/comments';
import { baziSections } from '@/data/sections';
import PostContent from '@/components/PostContent';
import { ArrowLeft, Share2 } from 'lucide-react';

export async function generateStaticParams() {
  return samplePosts.map((post) => ({
    id: post.id,
  }));
}

interface PostPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function PostPage({ params }: PostPageProps) {
  const { id } = await params;

  const post = samplePosts.find(p => p.id === id);
  if (!post) {
    notFound();
  }

  const postComments = sampleComments.filter(c => c.postId === post.id);
  const section = baziSections.find(s => s.key === post.category);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 sticky top-0 z-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <Link href={section ? `/${section.slug}` : '/forum'} className="flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors">
              <ArrowLeft className="w-5 h-5" />
              <span>返回板块</span>
            </Link>
            
            <div className="flex items-center space-x-4">
              <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors">
                <Share2 className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      </header>

      <PostContent post={post} comments={postComments} />
    </div>
  );
}
