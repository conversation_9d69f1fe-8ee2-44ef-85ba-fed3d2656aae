@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans), system-ui, -apple-system, sans-serif;
  scroll-behavior: smooth;
}

/* Custom components */
.card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6 transition-all duration-200;
}

.btn-primary {
  @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-2xl transition-colors shadow-sm hover:shadow-md;
}

.btn-secondary {
  @apply bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-2 px-4 rounded-2xl transition-colors shadow-sm hover:shadow-md;
}

.text-gradient {
  @apply bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent;
}

/* 八字特殊样式 */
.bazi-char {
  @apply text-4xl md:text-6xl font-bold text-gray-800 hover:text-blue-600 transition-colors cursor-pointer;
  font-family: "SimSun", "宋体", serif;
}

/* 文本截断 */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* 响应式设计优化 */
@media (max-width: 768px) {
  .bazi-char {
    @apply text-3xl;
  }

  .card {
    @apply p-3;
  }
}
