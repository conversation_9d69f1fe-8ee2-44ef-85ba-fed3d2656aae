import Link from 'next/link';
import { baziSections } from '@/data/sections';
import DynamicIcon from '@/components/DynamicIcon';

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-2">
              <h1 className="text-xl font-bold text-gradient">八字论坛</h1>
              <span className="text-sm text-gray-500">新闻自由 舆论监督</span>
            </div>
            <nav className="hidden md:flex space-x-6">
              <Link href="/about" className="text-gray-600 hover:text-blue-600 transition-colors">
                项目理念
              </Link>
              <Link href="/join" className="btn-primary">
                加入我们
              </Link>
            </nav>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 px-4 text-center">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            新闻自由 舆论监督
          </h2>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            不仅关乎社会，也关乎你我。当信息无法自由传播，我们所知道的就不一定是真相。
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/forum" className="btn-primary text-lg px-8 py-3">
              进入论坛
            </Link>
            <Link href="/about" className="btn-secondary text-lg px-8 py-3">
              了解更多
            </Link>
          </div>
        </div>
      </section>

      {/* Bazi Grid */}
      <section className="py-16 px-4">
        <div className="max-w-6xl mx-auto">
          <h3 className="text-3xl font-bold text-center mb-12 text-gray-900">
            八字论坛
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 md:gap-8">
            {baziSections.map((section) => (
              <Link
                key={section.key}
                href={`/section/${section.key}`}
                className="group"
              >
                <div className="card hover:shadow-lg transition-all duration-300 group-hover:scale-105 text-center h-full">
                  <div className="flex justify-center mb-4">
                    <DynamicIcon
                      name={section.icon}
                      className="w-12 h-12 text-gray-600 group-hover:text-blue-600 transition-colors"
                    />
                  </div>
                  <div className="bazi-char mb-2">{section.key}</div>
                  <h4 className="font-semibold text-gray-900 mb-2">{section.name}</h4>
                  <p className="text-sm text-gray-600">{section.description}</p>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12 px-4">
        <div className="max-w-4xl mx-auto text-center">
          <blockquote className="text-lg mb-6 italic">
            &ldquo;新闻自由，舆论监督——不仅关乎社会，也关乎你我。<br />
            当信息无法自由传播，我们所知道的就不一定是真相；<br />
            当公众无法表达质疑，错误就可能一再发生。<br />
            这些权利，是你我的日常保障。&rdquo;
          </blockquote>
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
            <Link href="/join" className="btn-primary">
              加入我们
            </Link>
            <Link href="/freemedia" className="btn-secondary bg-gray-700 hover:bg-gray-600 text-white">
              了解更多
            </Link>
          </div>
          <p className="text-gray-400 text-sm">
            © 2024 八字论坛. 致力于推动新闻自由与舆论监督.
          </p>
        </div>
      </footer>
    </div>
  );
}
