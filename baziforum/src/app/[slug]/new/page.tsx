import Link from 'next/link';
import { notFound } from 'next/navigation';
import { baziSections } from '@/data/sections';
import DynamicIcon from '@/components/DynamicIcon';
import NewPostForm from '@/components/NewPostForm';
import SideNavigation from '@/components/SideNavigation';
import { ArrowLeft } from 'lucide-react';

export async function generateStaticParams() {
  return baziSections.map((section) => ({
    slug: section.slug,
  }));
}

interface NewPostPageProps {
  params: Promise<{
    slug: string;
  }>;
}

export default async function NewPostPage({ params }: NewPostPageProps) {
  const { slug } = await params;

  const section = baziSections.find(s => s.slug === slug);
  if (!section) {
    notFound();
  }

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Side Navigation */}
      <SideNavigation currentSlug={section.slug} />

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <header className="bg-white border-b border-gray-200 sticky top-0 z-40">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <div className="flex items-center space-x-4 md:ml-0 ml-16">
                <Link
                  href={`/${section.slug}`}
                  className="flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors"
                >
                  <ArrowLeft className="w-5 h-5" />
                  <span className="hidden sm:inline">返回板块</span>
                </Link>
                <div className="h-6 w-px bg-gray-300" />
                <div className="flex items-center space-x-2">
                  <DynamicIcon
                    name={section.icon}
                    className="w-6 h-6 text-gray-600"
                  />
                  <span className="font-medium text-gray-900">
                    <span className="hidden sm:inline">发布到 </span>{section.name}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </header>

        <NewPostForm section={section} />
      </div>
    </div>
  );
}
