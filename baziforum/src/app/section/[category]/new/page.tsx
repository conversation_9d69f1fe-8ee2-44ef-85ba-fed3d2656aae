import Link from 'next/link';
import { notFound } from 'next/navigation';
import { baziSections } from '@/data/sections';
import DynamicIcon from '@/components/DynamicIcon';
import NewPostForm from '@/components/NewPostForm';
import { ArrowLeft } from 'lucide-react';

export async function generateStaticParams() {
  return baziSections.map((section) => ({
    category: section.key,
  }));
}

interface NewPostPageProps {
  params: Promise<{
    category: string;
  }>;
}

export default async function NewPostPage({ params }: NewPostPageProps) {
  const { category } = await params;

  const section = baziSections.find(s => s.key === category);
  if (!section) {
    notFound();
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 sticky top-0 z-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Link 
                href={`/section/${section.key}`} 
                className="flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors"
              >
                <ArrowLeft className="w-5 h-5" />
                <span>返回板块</span>
              </Link>
              <div className="h-6 w-px bg-gray-300" />
              <div className="flex items-center space-x-2">
                <DynamicIcon
                  name={section.icon}
                  className="w-6 h-6 text-gray-600"
                />
                <span className="font-medium text-gray-900">发布到 {section.name}</span>
              </div>
            </div>
          </div>
        </div>
      </header>

      <NewPostForm section={section} />
    </div>
  );
}
