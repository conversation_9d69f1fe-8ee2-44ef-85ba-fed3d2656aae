import Link from 'next/link';
import { baziSections } from '@/data/sections';
import { samplePosts } from '@/data/posts';
import PostCard from '@/components/PostCard';
import DynamicIcon from '@/components/DynamicIcon';
import { ArrowLeft, TrendingUp, Clock, Star } from 'lucide-react';

export default function ForumPage() {
  // 获取热门帖子（按点赞数+评论数+浏览数排序）
  const hotPosts = [...samplePosts]
    .sort((a, b) => (b.likes + b.comments + b.views) - (a.likes + a.comments + a.views))
    .slice(0, 5);

  // 获取最新帖子
  const latestPosts = [...samplePosts]
    .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
    .slice(0, 5);

  // 获取置顶帖子
  const stickyPosts = samplePosts.filter(post => post.isSticky);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Link href="/" className="flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors">
                <ArrowLeft className="w-5 h-5" />
                <span>返回首页</span>
              </Link>
              <div className="h-6 w-px bg-gray-300" />
              <h1 className="text-xl font-bold text-gray-900">八字论坛</h1>
            </div>
            
            <nav className="hidden md:flex space-x-6">
              <Link href="/about" className="text-gray-600 hover:text-blue-600 transition-colors">
                项目理念
              </Link>
              <Link href="/join" className="btn-primary">
                加入我们
              </Link>
            </nav>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid lg:grid-cols-4 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-3 space-y-8">
            {/* Sticky Posts */}
            {stickyPosts.length > 0 && (
              <section>
                <div className="flex items-center space-x-2 mb-4">
                  <Star className="w-5 h-5 text-orange-500" />
                  <h2 className="text-lg font-semibold text-gray-900">置顶公告</h2>
                </div>
                <div className="space-y-4">
                  {stickyPosts.map((post) => (
                    <PostCard key={post.id} post={post} />
                  ))}
                </div>
              </section>
            )}

            {/* Hot Posts */}
            <section>
              <div className="flex items-center space-x-2 mb-4">
                <TrendingUp className="w-5 h-5 text-red-500" />
                <h2 className="text-lg font-semibold text-gray-900">热门讨论</h2>
              </div>
              <div className="space-y-4">
                {hotPosts.map((post) => (
                  <PostCard key={post.id} post={post} />
                ))}
              </div>
            </section>

            {/* Latest Posts */}
            <section>
              <div className="flex items-center space-x-2 mb-4">
                <Clock className="w-5 h-5 text-blue-500" />
                <h2 className="text-lg font-semibold text-gray-900">最新发布</h2>
              </div>
              <div className="space-y-4">
                {latestPosts.map((post) => (
                  <PostCard key={post.id} post={post} />
                ))}
              </div>
            </section>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Sections Overview */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">板块导航</h3>
              <div className="space-y-3">
                {baziSections.map((section) => {
                  const sectionPostCount = samplePosts.filter(p => p.category === section.key).length;
                  return (
                    <Link
                      key={section.key}
                      href={`/section/${section.key}`}
                      className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors group"
                    >
                      <div className="flex items-center space-x-3">
                        <DynamicIcon
                          name={section.icon}
                          className="w-6 h-6 text-gray-600 group-hover:text-blue-600 transition-colors"
                        />
                        <div>
                          <div className="font-medium text-gray-900 group-hover:text-blue-600 transition-colors">
                            {section.key} - {section.name}
                          </div>
                          <div className="text-sm text-gray-500">
                            {section.description}
                          </div>
                        </div>
                      </div>
                      <span className="text-sm text-gray-400">
                        {sectionPostCount}
                      </span>
                    </Link>
                  );
                })}
              </div>
            </div>

            {/* Quick Stats */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">社区统计</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">总帖子数</span>
                  <span className="font-medium text-gray-900">{samplePosts.length}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">活跃板块</span>
                  <span className="font-medium text-gray-900">{baziSections.length}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">今日新增</span>
                  <span className="font-medium text-gray-900">3</span>
                </div>
              </div>
            </div>

            {/* About */}
            <div className="bg-blue-50 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-blue-900 mb-3">关于八字论坛</h3>
              <p className="text-blue-800 text-sm leading-relaxed mb-4">
                以&ldquo;新闻自由、舆论监督&rdquo;为核心价值的公共议题讨论平台，致力于推动理性对话与社会进步。
              </p>
              <Link href="/about" className="text-blue-600 hover:text-blue-700 text-sm font-medium">
                了解更多 →
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
