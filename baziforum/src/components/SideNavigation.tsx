'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { baziSections } from '@/data/sections';
import DynamicIcon from '@/components/DynamicIcon';
import { ChevronLeft, ChevronRight, Menu, X } from 'lucide-react';

interface SideNavigationProps {
  currentSlug?: string;
}

export default function SideNavigation({ currentSlug }: SideNavigationProps) {
  const [isExpanded, setIsExpanded] = useState(true); // 默认展开以便调试
  const [isMobileOpen, setIsMobileOpen] = useState(false);
  const pathname = usePathname();

  const toggleExpanded = () => setIsExpanded(!isExpanded);
  const toggleMobile = () => setIsMobileOpen(!isMobileOpen);

  const NavigationContent = () => (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          {(isExpanded || isMobileOpen) && (
            <h2 className="font-semibold text-gray-900">板块导航</h2>
          )}
          <button
            onClick={isExpanded ? toggleExpanded : toggleMobile}
            className="p-1 rounded-lg hover:bg-gray-100 transition-colors md:hidden"
          >
            {isMobileOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
          </button>
        </div>
      </div>

      {/* Navigation Links */}
      <nav className="flex-1 p-4 space-y-2">
        {baziSections.map((section) => {
          const isActive = currentSlug === section.slug || pathname === `/${section.slug}`;
          
          return (
            <Link
              key={section.key}
              href={`/${section.slug}`}
              className={`
                flex items-center space-x-3 p-3 rounded-lg transition-all duration-200
                ${isActive 
                  ? 'bg-blue-50 text-blue-600 border border-blue-200' 
                  : 'text-gray-700 hover:bg-gray-50 hover:text-blue-600'
                }
                ${!isExpanded && !isMobileOpen ? 'justify-center' : ''}
              `}
              title={!isExpanded && !isMobileOpen ? section.name : undefined}
            >
              <DynamicIcon
                name={section.icon}
                className={`w-5 h-5 flex-shrink-0 ${isActive ? 'text-blue-600' : ''}`}
              />
              {(isExpanded || isMobileOpen) && (
                <div className="flex-1 min-w-0">
                  <div className="font-medium text-sm">{section.name}</div>
                  <div className="text-xs text-gray-500 truncate">{section.description}</div>
                </div>
              )}
            </Link>
          );
        })}
      </nav>

      {/* Footer */}
      <div className="p-4 border-t border-gray-200">
        <div className="space-y-2">
          <Link
            href="/forum"
            className={`
              flex items-center space-x-3 p-2 rounded-lg transition-colors
              text-gray-600 hover:bg-gray-50 hover:text-blue-600
              ${!isExpanded && !isMobileOpen ? 'justify-center' : ''}
            `}
            title={!isExpanded && !isMobileOpen ? '论坛总览' : undefined}
          >
            <DynamicIcon name="Grid3X3" className="w-4 h-4" />
            {(isExpanded || isMobileOpen) && <span className="text-sm">论坛总览</span>}
          </Link>
          
          <Link
            href="/"
            className={`
              flex items-center space-x-3 p-2 rounded-lg transition-colors
              text-gray-600 hover:bg-gray-50 hover:text-blue-600
              ${!isExpanded && !isMobileOpen ? 'justify-center' : ''}
            `}
            title={!isExpanded && !isMobileOpen ? '返回首页' : undefined}
          >
            <DynamicIcon name="Home" className="w-4 h-4" />
            {(isExpanded || isMobileOpen) && <span className="text-sm">返回首页</span>}
          </Link>
        </div>
      </div>
    </div>
  );

  // 调试信息
  console.log('SideNavigation rendering:', { currentSlug, isExpanded, pathname });

  return (
    <>
      {/* Desktop Sidebar */}
      <div className={`
        flex flex-col bg-white border-r border-gray-200 transition-all duration-300 relative
        ${isExpanded ? 'w-80' : 'w-16'}
      `} style={{ backgroundColor: 'red', minHeight: '100vh' }}>
        <NavigationContent />
        
        {/* Expand/Collapse Button */}
        <button
          onClick={toggleExpanded}
          className="absolute top-4 -right-3 bg-white border border-gray-200 rounded-full p-1 shadow-sm hover:shadow-md transition-shadow z-10"
        >
          {isExpanded ? (
            <ChevronLeft className="w-4 h-4 text-gray-600" />
          ) : (
            <ChevronRight className="w-4 h-4 text-gray-600" />
          )}
        </button>
      </div>

      {/* Mobile Menu Button */}
      <button
        onClick={toggleMobile}
        className="md:hidden fixed top-4 left-4 z-50 bg-white border border-gray-200 rounded-lg p-2 shadow-sm"
      >
        <Menu className="w-5 h-5 text-gray-600" />
      </button>

      {/* Mobile Sidebar Overlay */}
      {isMobileOpen && (
        <div className="md:hidden fixed inset-0 z-40">
          <div className="absolute inset-0 bg-black bg-opacity-50" onClick={toggleMobile} />
          <div className="absolute left-0 top-0 h-full w-80 bg-white shadow-xl">
            <NavigationContent />
          </div>
        </div>
      )}
    </>
  );
}
