'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { baziSections } from '@/data/sections';
import DynamicIcon from '@/components/DynamicIcon';
import { ChevronLeft, ChevronRight, Menu, X, PanelLeftClose, PanelLeftOpen } from 'lucide-react';

interface SideNavigationProps {
  currentSlug?: string;
}

export default function SideNavigation({ currentSlug }: SideNavigationProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isMobileOpen, setIsMobileOpen] = useState(false);
  const pathname = usePathname();

  const toggleExpanded = () => setIsExpanded(!isExpanded);
  const toggleMobile = () => setIsMobileOpen(!isMobileOpen);

  const NavigationContent = () => (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        {(isExpanded || isMobileOpen) && (
          <h2 className="font-semibold text-gray-900">板块导航</h2>
        )}
      </div>

      {/* Navigation Links */}
      <nav className="flex-1 p-4 space-y-2">
        {baziSections.map((section) => {
          const isActive = currentSlug === section.slug || pathname === `/${section.slug}`;
          
          return (
            <Link
              key={section.key}
              href={`/${section.slug}`}
              className={`
                flex items-center space-x-3 p-3 rounded-lg transition-all duration-200
                ${isActive 
                  ? 'bg-blue-50 text-blue-600 border border-blue-200' 
                  : 'text-gray-700 hover:bg-gray-50 hover:text-blue-600'
                }
                ${!isExpanded && !isMobileOpen ? 'justify-center' : ''}
              `}
              title={!isExpanded && !isMobileOpen ? section.name : undefined}
            >
              <DynamicIcon
                name={section.icon}
                className={`w-5 h-5 flex-shrink-0 ${isActive ? 'text-blue-600' : ''}`}
              />
              {(isExpanded || isMobileOpen) && (
                <div className="flex-1 min-w-0">
                  <div className="font-medium text-sm">{section.name}</div>
                  <div className="text-xs text-gray-500 truncate">{section.description}</div>
                </div>
              )}
            </Link>
          );
        })}
      </nav>

      {/* Footer */}
      <div className="p-4 border-t border-gray-200">
        <div className="space-y-2">
          <Link
            href="/forum"
            className={`
              flex items-center space-x-3 p-2 rounded-lg transition-colors
              text-gray-600 hover:bg-gray-50 hover:text-blue-600
              ${!isExpanded && !isMobileOpen ? 'justify-center' : ''}
            `}
            title={!isExpanded && !isMobileOpen ? '论坛总览' : undefined}
          >
            <DynamicIcon name="Grid3X3" className="w-4 h-4" />
            {(isExpanded || isMobileOpen) && <span className="text-sm">论坛总览</span>}
          </Link>
          
          <Link
            href="/"
            className={`
              flex items-center space-x-3 p-2 rounded-lg transition-colors
              text-gray-600 hover:bg-gray-50 hover:text-blue-600
              ${!isExpanded && !isMobileOpen ? 'justify-center' : ''}
            `}
            title={!isExpanded && !isMobileOpen ? '返回首页' : undefined}
          >
            <DynamicIcon name="Home" className="w-4 h-4" />
            {(isExpanded || isMobileOpen) && <span className="text-sm">返回首页</span>}
          </Link>
        </div>
      </div>
    </div>
  );

  return (
    <>
      {/* Desktop Sidebar */}
      <div className={`
        hidden lg:flex flex-col bg-white border-r border-gray-200 transition-all duration-300 relative
        ${isExpanded ? 'w-80' : 'w-16'}
      `}>
        <NavigationContent />
        
        {/* Expand/Collapse Button */}
        <button
          onClick={toggleExpanded}
          className={`
            absolute top-1/2 -translate-y-1/2 -right-4
            ${isExpanded
              ? 'bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-xl'
              : 'bg-green-600 hover:bg-green-700 text-white shadow-lg hover:shadow-xl animate-pulse'
            }
            rounded-full p-2.5 transition-all duration-200 z-10
            border-2 border-white
          `}
          title={isExpanded ? '收起导航栏' : '展开导航栏'}
        >
          {isExpanded ? (
            <PanelLeftClose className="w-5 h-5" />
          ) : (
            <PanelLeftOpen className="w-5 h-5" />
          )}
        </button>
      </div>

      {/* Mobile Menu Button */}
      <button
        onClick={toggleMobile}
        className={`
          lg:hidden fixed top-4 left-4 z-50
          ${isMobileOpen
            ? 'bg-red-600 hover:bg-red-700 text-white'
            : 'bg-blue-600 hover:bg-blue-700 text-white'
          }
          rounded-full p-2.5 shadow-lg hover:shadow-xl transition-all duration-200
          border-2 border-white
        `}
        title={isMobileOpen ? '关闭菜单' : '打开菜单'}
      >
        {isMobileOpen ? (
          <X className="w-5 h-5" />
        ) : (
          <Menu className="w-5 h-5" />
        )}
      </button>

      {/* Mobile Sidebar Overlay */}
      {isMobileOpen && (
        <div className="lg:hidden fixed inset-0 z-40">
          <div className="absolute inset-0 bg-black bg-opacity-50" onClick={toggleMobile} />
          <div className="absolute left-0 top-0 h-full w-80 bg-white shadow-xl">
            <NavigationContent />
          </div>
        </div>
      )}
    </>
  );
}
