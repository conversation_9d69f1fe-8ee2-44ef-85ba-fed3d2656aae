'use client';

import { useState } from 'react';
import Link from 'next/link';
import { BaziSection, Post, TabType } from '@/types';
import PostCard from '@/components/PostCard';
import { Plus } from 'lucide-react';

interface SectionContentProps {
  section: BaziSection;
  posts: Post[];
}

export default function SectionContent({ section, posts }: SectionContentProps) {
  const [activeTab, setActiveTab] = useState<TabType>('hot');

  // 根据标签页排序帖子
  const sortedPosts = [...posts].sort((a, b) => {
    if (activeTab === 'hot') {
      // 置顶帖子优先，然后按热度排序
      if (a.isSticky && !b.isSticky) return -1;
      if (!a.isSticky && b.isSticky) return 1;
      return (b.likes + b.comments + b.views) - (a.likes + a.comments + a.views);
    } else if (activeTab === 'latest') {
      // 按时间排序
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    } else {
      // 我的帖子 - 这里暂时返回空数组，因为没有用户系统
      return 0;
    }
  });

  const tabs = [
    { key: 'hot' as TabType, label: '热门', count: posts.length },
    { key: 'latest' as TabType, label: '最新', count: posts.length },
    { key: 'mine' as TabType, label: '我的', count: 0 }
  ];

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Tabs */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div className="flex border-b border-gray-200">
          {tabs.map((tab) => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key)}
              className={`flex-1 px-6 py-4 text-sm font-medium transition-colors ${
                activeTab === tab.key
                  ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50'
                  : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
              }`}
            >
              {tab.label}
              <span className="ml-2 px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded-full">
                {tab.count}
              </span>
            </button>
          ))}
        </div>
      </div>

      {/* Posts */}
      <div className="space-y-4">
        {activeTab === 'mine' ? (
          <div className="text-center py-12">
            <div className="text-gray-400 text-lg mb-2">暂无内容</div>
            <p className="text-gray-500 text-sm">登录后可查看您发布的帖子</p>
          </div>
        ) : sortedPosts.length > 0 ? (
          sortedPosts.map((post) => (
            <PostCard key={post.id} post={post} />
          ))
        ) : (
          <div className="text-center py-12">
            <div className="text-gray-400 text-lg mb-2">暂无帖子</div>
            <p className="text-gray-500 text-sm">成为第一个在此板块发帖的人吧！</p>
            <Link href={`/${section.slug}/new`} className="btn-primary mt-4 inline-flex items-center space-x-2">
              <Plus className="w-4 h-4" />
              <span>发布帖子</span>
            </Link>
          </div>
        )}
      </div>
    </div>
  );
}
