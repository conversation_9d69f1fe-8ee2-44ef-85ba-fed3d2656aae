# 八字论坛 - 新闻自由 舆论监督

面向大学生的公共议题内容型社交平台，以"新闻自由、舆论监督"为核心价值。

## 项目概述

这是一个Demo版本，旨在展示平台理念、结构和部分交互功能。我们以"新、闻、自、由、舆、论、监、督"八个字构建论坛结构，每个字代表一个重要的讨论领域。

### 八字板块

- **新** - 最新事件：浏览当前社会热点内容
- **闻** - 重大事件档案：汇总关键事件，构成"公共记忆"
- **自** - 个人观点发表：自由表达区
- **由** - 假新闻溯源：提供谣言分析、信息溯源内容
- **舆** - 媒体观察：分析媒体偏向、舆论塑造机制
- **论** - 话题辩论场：开设辩题，引导参与讨论
- **监** - 举报与公审：用户举报展示，社区自治
- **督** - 社区进展公告：开发日志、任务列表、人员招募

## 技术栈

- **前端框架**: Next.js 15 + React 18
- **样式**: Tailwind CSS
- **图标**: Lucide React
- **UI组件**: Headless UI
- **语言**: TypeScript
- **部署**: Cloudflare Pages

## 开始使用

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

打开 [http://localhost:3000](http://localhost:3000) 查看网站。

### 构建生产版本

```bash
npm run build
```

## 项目结构

```
src/
├── app/                    # Next.js App Router
│   ├── about/             # 关于页面
│   ├── forum/             # 论坛总览
│   ├── join/              # 加入我们
│   ├── post/[id]/         # 帖子详情
│   ├── section/[category]/ # 板块页面
│   └── globals.css        # 全局样式
├── components/            # 可复用组件
├── data/                  # 示例数据
├── lib/                   # 工具函数
└── types/                 # TypeScript 类型定义
```

## 功能特性

- ✅ 响应式设计，移动端友好
- ✅ 八字板块结构化内容展示
- ✅ 帖子浏览、详情查看
- ✅ 模拟发帖功能
- ✅ 评论系统（前端展示）
- ✅ 点赞、收藏等交互
- ✅ 加入我们表单
- ✅ 项目理念介绍

## 部署到 Cloudflare Pages

1. 将代码推送到 GitHub 仓库
2. 在 Cloudflare Pages 中连接仓库
3. 设置构建命令：`npm run build`
4. 设置输出目录：`out`（如果使用静态导出）
5. 部署完成

## 贡献指南

我们欢迎以下类型的贡献者：

- **前端开发者**: React/Next.js 开发
- **内容策划**: 公共议题内容创作
- **社群志愿者**: 社区运营和用户支持

请访问 `/join` 页面了解如何加入我们。

## 许可证

MIT License

## 联系我们

如果您对项目有任何问题或建议，欢迎通过以下方式联系：

- 项目网站: [八字论坛](https://your-domain.pages.dev)
- 邮箱: <EMAIL>

---

**新闻自由，舆论监督——不仅关乎社会，也关乎你我。**
