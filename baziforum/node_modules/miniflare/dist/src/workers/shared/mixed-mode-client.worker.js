// src/workers/shared/mixed-mode-client.worker.ts
import { WorkerEntrypoint } from "cloudflare:workers";
var Client = class extends WorkerEntrypoint {
  async fetch(request) {
    let proxiedHeaders = new Headers();
    for (let [name, value] of request.headers)
      name === "upgrade" || name.startsWith("MF-") ? proxiedHeaders.set(name, value) : proxiedHeaders.set(`MF-Header-${name}`, value);
    proxiedHeaders.set("MF-URL", request.url), proxiedHeaders.set("MF-Binding", this.env.binding);
    let req = new Request(request, {
      headers: proxiedHeaders
    });
    return fetch(this.env.mixedModeConnectionString, req);
  }
};
export {
  Client as default
};
//# sourceMappingURL=mixed-mode-client.worker.js.map
