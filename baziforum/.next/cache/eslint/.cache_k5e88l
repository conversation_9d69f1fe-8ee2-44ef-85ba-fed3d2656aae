[{"/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/app/about/page.tsx": "1", "/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/app/forum/page.tsx": "2", "/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/app/join/page.tsx": "3", "/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/app/layout.tsx": "4", "/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/app/not-found.tsx": "5", "/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/app/page.tsx": "6", "/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/app/post/[id]/page.tsx": "7", "/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/app/section/[category]/new/page.tsx": "8", "/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/app/section/[category]/page.tsx": "9", "/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/components/PostCard.tsx": "10", "/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/data/comments.ts": "11", "/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/data/posts.ts": "12", "/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/data/sections.ts": "13", "/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/lib/utils.ts": "14", "/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/types/index.ts": "15"}, {"size": 6815, "mtime": 1749014643640, "results": "16", "hashOfConfig": "17"}, {"size": 6893, "mtime": 1749014728792, "results": "18", "hashOfConfig": "17"}, {"size": 9013, "mtime": 1749014672695, "results": "19", "hashOfConfig": "17"}, {"size": 865, "mtime": 1749014436901, "results": "20", "hashOfConfig": "17"}, {"size": 990, "mtime": 1749014766027, "results": "21", "hashOfConfig": "17"}, {"size": 4031, "mtime": 1749014498567, "results": "22", "hashOfConfig": "17"}, {"size": 7776, "mtime": 1749014611567, "results": "23", "hashOfConfig": "17"}, {"size": 9082, "mtime": 1749014703690, "results": "24", "hashOfConfig": "17"}, {"size": 4887, "mtime": 1749014586571, "results": "25", "hashOfConfig": "17"}, {"size": 2981, "mtime": 1749014564546, "results": "26", "hashOfConfig": "17"}, {"size": 1459, "mtime": 1749014407712, "results": "27", "hashOfConfig": "17"}, {"size": 5698, "mtime": 1749014396589, "results": "28", "hashOfConfig": "17"}, {"size": 1474, "mtime": 1749014371165, "results": "29", "hashOfConfig": "17"}, {"size": 1196, "mtime": 1749014416049, "results": "30", "hashOfConfig": "17"}, {"size": 1105, "mtime": 1749014357249, "results": "31", "hashOfConfig": "17"}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 8, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "tlu3pl", {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/app/about/page.tsx", ["77", "78", "79", "80", "81", "82", "83", "84"], [], "/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/app/forum/page.tsx", ["85", "86"], [], "/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/app/join/page.tsx", [], [], "/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/app/layout.tsx", [], [], "/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/app/not-found.tsx", [], [], "/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/app/page.tsx", ["87", "88"], [], "/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/app/post/[id]/page.tsx", [], [], "/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/app/section/[category]/new/page.tsx", ["89", "90"], [], "/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/app/section/[category]/page.tsx", ["91"], [], "/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/components/PostCard.tsx", [], [], "/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/data/comments.ts", [], [], "/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/data/posts.ts", [], [], "/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/data/sections.ts", [], [], "/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/lib/utils.ts", [], [], "/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/types/index.ts", [], [], {"ruleId": "92", "severity": 2, "message": "93", "line": 34, "column": 15, "nodeType": "94", "messageId": "95", "suggestions": "96"}, {"ruleId": "92", "severity": 2, "message": "93", "line": 34, "column": 59, "nodeType": "94", "messageId": "95", "suggestions": "97"}, {"ruleId": "92", "severity": 2, "message": "93", "line": 78, "column": 26, "nodeType": "94", "messageId": "95", "suggestions": "98"}, {"ruleId": "92", "severity": 2, "message": "93", "line": 78, "column": 29, "nodeType": "94", "messageId": "95", "suggestions": "99"}, {"ruleId": "92", "severity": 2, "message": "93", "line": 79, "column": 38, "nodeType": "94", "messageId": "95", "suggestions": "100"}, {"ruleId": "92", "severity": 2, "message": "93", "line": 79, "column": 45, "nodeType": "94", "messageId": "95", "suggestions": "101"}, {"ruleId": "92", "severity": 2, "message": "93", "line": 86, "column": 20, "nodeType": "94", "messageId": "95", "suggestions": "102"}, {"ruleId": "92", "severity": 2, "message": "93", "line": 86, "column": 36, "nodeType": "94", "messageId": "95", "suggestions": "103"}, {"ruleId": "92", "severity": 2, "message": "93", "line": 151, "column": 18, "nodeType": "94", "messageId": "95", "suggestions": "104"}, {"ruleId": "92", "severity": 2, "message": "93", "line": 151, "column": 28, "nodeType": "94", "messageId": "95", "suggestions": "105"}, {"ruleId": "92", "severity": 2, "message": "93", "line": 76, "column": 13, "nodeType": "94", "messageId": "95", "suggestions": "106"}, {"ruleId": "92", "severity": 2, "message": "93", "line": 79, "column": 27, "nodeType": "94", "messageId": "95", "suggestions": "107"}, {"ruleId": "108", "severity": 2, "message": "109", "line": 8, "column": 10, "nodeType": null, "messageId": "110", "endLine": 8, "endColumn": 22}, {"ruleId": "108", "severity": 2, "message": "111", "line": 18, "column": 9, "nodeType": null, "messageId": "110", "endLine": 18, "endColumn": 15}, {"ruleId": "108", "severity": 2, "message": "109", "line": 8, "column": 10, "nodeType": null, "messageId": "110", "endLine": 8, "endColumn": 22}, "react/no-unescaped-entities", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", "JSXText", "unescapedEntityAlts", ["112", "113", "114", "115"], ["116", "117", "118", "119"], ["120", "121", "122", "123"], ["124", "125", "126", "127"], ["128", "129", "130", "131"], ["132", "133", "134", "135"], ["136", "137", "138", "139"], ["140", "141", "142", "143"], ["144", "145", "146", "147"], ["148", "149", "150", "151"], ["152", "153", "154", "155"], ["156", "157", "158", "159"], "@typescript-eslint/no-unused-vars", "'BaziCategory' is defined but never used.", "unusedVar", "'router' is assigned a value but never used.", {"messageId": "160", "data": "161", "fix": "162", "desc": "163"}, {"messageId": "160", "data": "164", "fix": "165", "desc": "166"}, {"messageId": "160", "data": "167", "fix": "168", "desc": "169"}, {"messageId": "160", "data": "170", "fix": "171", "desc": "172"}, {"messageId": "160", "data": "173", "fix": "174", "desc": "163"}, {"messageId": "160", "data": "175", "fix": "176", "desc": "166"}, {"messageId": "160", "data": "177", "fix": "178", "desc": "169"}, {"messageId": "160", "data": "179", "fix": "180", "desc": "172"}, {"messageId": "160", "data": "181", "fix": "182", "desc": "163"}, {"messageId": "160", "data": "183", "fix": "184", "desc": "166"}, {"messageId": "160", "data": "185", "fix": "186", "desc": "169"}, {"messageId": "160", "data": "187", "fix": "188", "desc": "172"}, {"messageId": "160", "data": "189", "fix": "190", "desc": "163"}, {"messageId": "160", "data": "191", "fix": "192", "desc": "166"}, {"messageId": "160", "data": "193", "fix": "194", "desc": "169"}, {"messageId": "160", "data": "195", "fix": "196", "desc": "172"}, {"messageId": "160", "data": "197", "fix": "198", "desc": "163"}, {"messageId": "160", "data": "199", "fix": "200", "desc": "166"}, {"messageId": "160", "data": "201", "fix": "202", "desc": "169"}, {"messageId": "160", "data": "203", "fix": "204", "desc": "172"}, {"messageId": "160", "data": "205", "fix": "206", "desc": "163"}, {"messageId": "160", "data": "207", "fix": "208", "desc": "166"}, {"messageId": "160", "data": "209", "fix": "210", "desc": "169"}, {"messageId": "160", "data": "211", "fix": "212", "desc": "172"}, {"messageId": "160", "data": "213", "fix": "214", "desc": "163"}, {"messageId": "160", "data": "215", "fix": "216", "desc": "166"}, {"messageId": "160", "data": "217", "fix": "218", "desc": "169"}, {"messageId": "160", "data": "219", "fix": "220", "desc": "172"}, {"messageId": "160", "data": "221", "fix": "222", "desc": "163"}, {"messageId": "160", "data": "223", "fix": "224", "desc": "166"}, {"messageId": "160", "data": "225", "fix": "226", "desc": "169"}, {"messageId": "160", "data": "227", "fix": "228", "desc": "172"}, {"messageId": "160", "data": "229", "fix": "230", "desc": "163"}, {"messageId": "160", "data": "231", "fix": "232", "desc": "166"}, {"messageId": "160", "data": "233", "fix": "234", "desc": "169"}, {"messageId": "160", "data": "235", "fix": "236", "desc": "172"}, {"messageId": "160", "data": "237", "fix": "238", "desc": "163"}, {"messageId": "160", "data": "239", "fix": "240", "desc": "166"}, {"messageId": "160", "data": "241", "fix": "242", "desc": "169"}, {"messageId": "160", "data": "243", "fix": "244", "desc": "172"}, {"messageId": "160", "data": "245", "fix": "246", "desc": "163"}, {"messageId": "160", "data": "247", "fix": "248", "desc": "166"}, {"messageId": "160", "data": "249", "fix": "250", "desc": "169"}, {"messageId": "160", "data": "251", "fix": "252", "desc": "172"}, {"messageId": "160", "data": "253", "fix": "254", "desc": "163"}, {"messageId": "160", "data": "255", "fix": "256", "desc": "166"}, {"messageId": "160", "data": "257", "fix": "258", "desc": "169"}, {"messageId": "160", "data": "259", "fix": "260", "desc": "172"}, "replaceWithAlt", {"alt": "261"}, {"range": "262", "text": "263"}, "Replace with `&quot;`.", {"alt": "264"}, {"range": "265", "text": "266"}, "Replace with `&ldquo;`.", {"alt": "267"}, {"range": "268", "text": "269"}, "Replace with `&#34;`.", {"alt": "270"}, {"range": "271", "text": "272"}, "Replace with `&rdquo;`.", {"alt": "261"}, {"range": "273", "text": "274"}, {"alt": "264"}, {"range": "275", "text": "276"}, {"alt": "267"}, {"range": "277", "text": "278"}, {"alt": "270"}, {"range": "279", "text": "280"}, {"alt": "261"}, {"range": "281", "text": "282"}, {"alt": "264"}, {"range": "283", "text": "284"}, {"alt": "267"}, {"range": "285", "text": "286"}, {"alt": "270"}, {"range": "287", "text": "288"}, {"alt": "261"}, {"range": "289", "text": "290"}, {"alt": "264"}, {"range": "291", "text": "292"}, {"alt": "267"}, {"range": "293", "text": "294"}, {"alt": "270"}, {"range": "295", "text": "296"}, {"alt": "261"}, {"range": "297", "text": "298"}, {"alt": "264"}, {"range": "299", "text": "300"}, {"alt": "267"}, {"range": "301", "text": "302"}, {"alt": "270"}, {"range": "303", "text": "304"}, {"alt": "261"}, {"range": "305", "text": "306"}, {"alt": "264"}, {"range": "307", "text": "308"}, {"alt": "267"}, {"range": "309", "text": "310"}, {"alt": "270"}, {"range": "311", "text": "312"}, {"alt": "261"}, {"range": "313", "text": "314"}, {"alt": "264"}, {"range": "315", "text": "316"}, {"alt": "267"}, {"range": "317", "text": "318"}, {"alt": "270"}, {"range": "319", "text": "320"}, {"alt": "261"}, {"range": "321", "text": "322"}, {"alt": "264"}, {"range": "323", "text": "324"}, {"alt": "267"}, {"range": "325", "text": "326"}, {"alt": "270"}, {"range": "327", "text": "328"}, {"alt": "261"}, {"range": "329", "text": "330"}, {"alt": "264"}, {"range": "331", "text": "332"}, {"alt": "267"}, {"range": "333", "text": "334"}, {"alt": "270"}, {"range": "335", "text": "336"}, {"alt": "261"}, {"range": "337", "text": "338"}, {"alt": "264"}, {"range": "339", "text": "340"}, {"alt": "267"}, {"range": "341", "text": "342"}, {"alt": "270"}, {"range": "343", "text": "344"}, {"alt": "261"}, {"range": "345", "text": "346"}, {"alt": "264"}, {"range": "347", "text": "348"}, {"alt": "267"}, {"range": "349", "text": "350"}, {"alt": "270"}, {"range": "351", "text": "352"}, {"alt": "261"}, {"range": "353", "text": "354"}, {"alt": "264"}, {"range": "355", "text": "356"}, {"alt": "267"}, {"range": "357", "text": "358"}, {"alt": "270"}, {"range": "359", "text": "360"}, "&quot;", [1264, 1337], "\n              &quot;在我们的生活中，每一次知情、发声、参与的机会，背后都依赖一个基础：信息公开与表达自由。\"\n            ", "&ldquo;", [1264, 1337], "\n              &ldquo;在我们的生活中，每一次知情、发声、参与的机会，背后都依赖一个基础：信息公开与表达自由。\"\n            ", "&#34;", [1264, 1337], "\n              &#34;在我们的生活中，每一次知情、发声、参与的机会，背后都依赖一个基础：信息公开与表达自由。\"\n            ", "&rdquo;", [1264, 1337], "\n              &rdquo;在我们的生活中，每一次知情、发声、参与的机会，背后都依赖一个基础：信息公开与表达自由。\"\n            ", [1264, 1337], "\n              \"在我们的生活中，每一次知情、发声、参与的机会，背后都依赖一个基础：信息公开与表达自由。&quot;\n            ", [1264, 1337], "\n              \"在我们的生活中，每一次知情、发声、参与的机会，背后都依赖一个基础：信息公开与表达自由。&ldquo;\n            ", [1264, 1337], "\n              \"在我们的生活中，每一次知情、发声、参与的机会，背后都依赖一个基础：信息公开与表达自由。&#34;\n            ", [1264, 1337], "\n              \"在我们的生活中，每一次知情、发声、参与的机会，背后都依赖一个基础：信息公开与表达自由。&rdquo;\n            ", [3135, 3166], "\n                若缺乏这些制度性的&quot;耳目\"，", [3135, 3166], "\n                若缺乏这些制度性的&ldquo;耳目\"，", [3135, 3166], "\n                若缺乏这些制度性的&#34;耳目\"，", [3135, 3166], "\n                若缺乏这些制度性的&rdquo;耳目\"，", [3135, 3166], "\n                若缺乏这些制度性的\"耳目&quot;，", [3135, 3166], "\n                若缺乏这些制度性的\"耳目&ldquo;，", [3135, 3166], "\n                若缺乏这些制度性的\"耳目&#34;，", [3135, 3166], "\n                若缺乏这些制度性的\"耳目&rdquo;，", [3210, 3294], "\n                我们建设这个平台，正是希望唤起更多人对这些&quot;看不见但重要\"的机制的理解，并亲自参与到这场时代的对话中来。\n              ", [3210, 3294], "\n                我们建设这个平台，正是希望唤起更多人对这些&ldquo;看不见但重要\"的机制的理解，并亲自参与到这场时代的对话中来。\n              ", [3210, 3294], "\n                我们建设这个平台，正是希望唤起更多人对这些&#34;看不见但重要\"的机制的理解，并亲自参与到这场时代的对话中来。\n              ", [3210, 3294], "\n                我们建设这个平台，正是希望唤起更多人对这些&rdquo;看不见但重要\"的机制的理解，并亲自参与到这场时代的对话中来。\n              ", [3210, 3294], "\n                我们建设这个平台，正是希望唤起更多人对这些\"看不见但重要&quot;的机制的理解，并亲自参与到这场时代的对话中来。\n              ", [3210, 3294], "\n                我们建设这个平台，正是希望唤起更多人对这些\"看不见但重要&ldquo;的机制的理解，并亲自参与到这场时代的对话中来。\n              ", [3210, 3294], "\n                我们建设这个平台，正是希望唤起更多人对这些\"看不见但重要&#34;的机制的理解，并亲自参与到这场时代的对话中来。\n              ", [3210, 3294], "\n                我们建设这个平台，正是希望唤起更多人对这些\"看不见但重要&rdquo;的机制的理解，并亲自参与到这场时代的对话中来。\n              ", [3499, 3576], "\n                我们以&quot;新、闻、自、由、舆、论、监、督\"八个字构建论坛结构，每个字代表一个重要的讨论领域：\n              ", [3499, 3576], "\n                我们以&ldquo;新、闻、自、由、舆、论、监、督\"八个字构建论坛结构，每个字代表一个重要的讨论领域：\n              ", [3499, 3576], "\n                我们以&#34;新、闻、自、由、舆、论、监、督\"八个字构建论坛结构，每个字代表一个重要的讨论领域：\n              ", [3499, 3576], "\n                我们以&rdquo;新、闻、自、由、舆、论、监、督\"八个字构建论坛结构，每个字代表一个重要的讨论领域：\n              ", [3499, 3576], "\n                我们以\"新、闻、自、由、舆、论、监、督&quot;八个字构建论坛结构，每个字代表一个重要的讨论领域：\n              ", [3499, 3576], "\n                我们以\"新、闻、自、由、舆、论、监、督&ldquo;八个字构建论坛结构，每个字代表一个重要的讨论领域：\n              ", [3499, 3576], "\n                我们以\"新、闻、自、由、舆、论、监、督&#34;八个字构建论坛结构，每个字代表一个重要的讨论领域：\n              ", [3499, 3576], "\n                我们以\"新、闻、自、由、舆、论、监、督&rdquo;八个字构建论坛结构，每个字代表一个重要的讨论领域：\n              ", [6324, 6398], "\n                以&quot;新闻自由、舆论监督\"为核心价值的公共议题讨论平台，致力于推动理性对话与社会进步。\n              ", [6324, 6398], "\n                以&ldquo;新闻自由、舆论监督\"为核心价值的公共议题讨论平台，致力于推动理性对话与社会进步。\n              ", [6324, 6398], "\n                以&#34;新闻自由、舆论监督\"为核心价值的公共议题讨论平台，致力于推动理性对话与社会进步。\n              ", [6324, 6398], "\n                以&rdquo;新闻自由、舆论监督\"为核心价值的公共议题讨论平台，致力于推动理性对话与社会进步。\n              ", [6324, 6398], "\n                以\"新闻自由、舆论监督&quot;为核心价值的公共议题讨论平台，致力于推动理性对话与社会进步。\n              ", [6324, 6398], "\n                以\"新闻自由、舆论监督&ldquo;为核心价值的公共议题讨论平台，致力于推动理性对话与社会进步。\n              ", [6324, 6398], "\n                以\"新闻自由、舆论监督&#34;为核心价值的公共议题讨论平台，致力于推动理性对话与社会进步。\n              ", [6324, 6398], "\n                以\"新闻自由、舆论监督&rdquo;为核心价值的公共议题讨论平台，致力于推动理性对话与社会进步。\n              ", [2997, 3035], "\n            &quot;新闻自由，舆论监督——不仅关乎社会，也关乎你我。", [2997, 3035], "\n            &ldquo;新闻自由，舆论监督——不仅关乎社会，也关乎你我。", [2997, 3035], "\n            &#34;新闻自由，舆论监督——不仅关乎社会，也关乎你我。", [2997, 3035], "\n            &rdquo;新闻自由，舆论监督——不仅关乎社会，也关乎你我。", [3123, 3162], "\n            这些权利，是你我的日常保障。&quot;\n          ", [3123, 3162], "\n            这些权利，是你我的日常保障。&ldquo;\n          ", [3123, 3162], "\n            这些权利，是你我的日常保障。&#34;\n          ", [3123, 3162], "\n            这些权利，是你我的日常保障。&rdquo;\n          "]