(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[335],{7550:(e,r,s)=>{"use strict";s.d(r,{A:()=>l});let l=(0,s(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},9203:(e,r,s)=>{Promise.resolve().then(s.bind(s,9931))},9931:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>h});var l=s(5155),a=s(2115),t=s(6874),n=s.n(t),i=s(9946);let c=(0,i.A)("code",[["path",{d:"m16 18 6-6-6-6",key:"eg8j8"}],["path",{d:"m8 6-6 6 6 6",key:"ppft3o"}]]),d=(0,i.A)("pen-tool",[["path",{d:"M15.707 21.293a1 1 0 0 1-1.414 0l-1.586-1.586a1 1 0 0 1 0-1.414l5.586-5.586a1 1 0 0 1 1.414 0l1.586 1.586a1 1 0 0 1 0 1.414z",key:"nt11vn"}],["path",{d:"m18 13-1.375-6.874a1 1 0 0 0-.746-.776L3.235 2.028a1 1 0 0 0-1.207 1.207L5.35 15.879a1 1 0 0 0 .776.746L13 18",key:"15qc1e"}],["path",{d:"m2.3 2.3 7.286 7.286",key:"1wuzzi"}],["circle",{cx:"11",cy:"11",r:"2",key:"xmgehs"}]]),o=(0,i.A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]);var x=s(7550);let m=(0,i.A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]);function h(){var e;let[r,s]=(0,a.useState)({name:"",email:"",role:"developer",message:"",skills:[],experience:""}),[t,i]=(0,a.useState)(!1),h=[{key:"developer",title:"前端开发",description:"React/Next.js方向，负责平台功能开发",icon:c,skills:["React","Next.js","TypeScript","Tailwind CSS"]},{key:"content",title:"内容策划",description:"公共议题关注者，负责内容策划与写作",icon:d,skills:["新闻写作","议题分析","内容策划","社会观察"]},{key:"volunteer",title:"社群志愿者",description:"协助社区运营、用户支持等工作",icon:o,skills:["社区运营","用户支持","活动策划","沟通协调"]}],u=e=>{s(r=>{var s;return{...r,skills:(null==(s=r.skills)?void 0:s.includes(e))?r.skills.filter(r=>r!==e):[...r.skills||[],e]}})};return t?(0,l.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,l.jsxs)("div",{className:"max-w-md mx-auto bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center",children:[(0,l.jsx)("div",{className:"text-green-500 text-6xl mb-4",children:"✓"}),(0,l.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"申请已提交"}),(0,l.jsx)("p",{className:"text-gray-600 mb-6",children:"感谢您的申请！我们会在3个工作日内通过邮件与您联系。"}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsx)(n(),{href:"/",className:"btn-primary w-full",children:"返回首页"}),(0,l.jsx)(n(),{href:"/about",className:"btn-secondary w-full",children:"了解更多"})]})]})}):(0,l.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,l.jsx)("header",{className:"bg-white border-b border-gray-200",children:(0,l.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,l.jsx)("div",{className:"flex items-center h-16",children:(0,l.jsxs)(n(),{href:"/",className:"flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors",children:[(0,l.jsx)(x.A,{className:"w-5 h-5"}),(0,l.jsx)("span",{children:"返回首页"})]})})})}),(0,l.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,l.jsxs)("div",{className:"text-center mb-12",children:[(0,l.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"加入我们"}),(0,l.jsx)("p",{className:"text-xl text-gray-600",children:"一起推动新闻自由与舆论监督的发展"})]}),(0,l.jsx)("div",{className:"grid lg:grid-cols-3 gap-8 mb-12",children:h.map(e=>{let a=e.icon;return(0,l.jsx)("div",{className:"card cursor-pointer transition-all ".concat(r.role===e.key?"ring-2 ring-blue-500 bg-blue-50":"hover:shadow-md"),onClick:()=>s(r=>({...r,role:e.key})),children:(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)(a,{className:"w-12 h-12 mx-auto mb-4 text-blue-600"}),(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:e.title}),(0,l.jsx)("p",{className:"text-gray-600 text-sm mb-4",children:e.description}),(0,l.jsx)("div",{className:"flex flex-wrap gap-1 justify-center",children:e.skills.map(e=>(0,l.jsx)("span",{className:"px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded",children:e},e))})]})},e.key)})}),(0,l.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-8",children:[(0,l.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"申请表单"}),(0,l.jsxs)("form",{onSubmit:e=>{e.preventDefault(),console.log("Form submitted:",r),i(!0)},className:"space-y-6",children:[(0,l.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"姓名 *"}),(0,l.jsx)("input",{type:"text",required:!0,value:r.name,onChange:e=>s(r=>({...r,name:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请输入您的姓名"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"邮箱 *"}),(0,l.jsx)("input",{type:"email",required:!0,value:r.email,onChange:e=>s(r=>({...r,email:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请输入您的邮箱"})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"相关技能"}),(0,l.jsx)("div",{className:"flex flex-wrap gap-2",children:null==(e=h.find(e=>e.key===r.role))?void 0:e.skills.map(e=>{var s;return(0,l.jsx)("button",{type:"button",onClick:()=>u(e),className:"px-3 py-1 text-sm rounded-full transition-colors ".concat((null==(s=r.skills)?void 0:s.includes(e))?"bg-blue-100 text-blue-700 border border-blue-300":"bg-gray-100 text-gray-600 border border-gray-300 hover:bg-gray-200"),children:e},e)})})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"相关经验"}),(0,l.jsx)("textarea",{value:r.experience,onChange:e=>s(r=>({...r,experience:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",rows:3,placeholder:"请简述您的相关经验（可选）"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"申请理由 *"}),(0,l.jsx)("textarea",{required:!0,value:r.message,onChange:e=>s(r=>({...r,message:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",rows:4,placeholder:"请告诉我们您为什么想要加入我们，以及您希望为项目贡献什么..."})]}),(0,l.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,l.jsx)("button",{type:"submit",className:"btn-primary flex-1",children:"提交申请"}),(0,l.jsxs)("a",{href:"https://forms.gle/example",target:"_blank",rel:"noopener noreferrer",className:"btn-secondary flex-1 flex items-center justify-center space-x-2",children:[(0,l.jsx)("span",{children:"Google Forms"}),(0,l.jsx)(m,{className:"w-4 h-4"})]})]})]})]})]})]})}},9946:(e,r,s)=>{"use strict";s.d(r,{A:()=>x});var l=s(2115);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),t=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,s)=>s?s.toUpperCase():r.toLowerCase()),n=e=>{let r=t(e);return r.charAt(0).toUpperCase()+r.slice(1)},i=function(){for(var e=arguments.length,r=Array(e),s=0;s<e;s++)r[s]=arguments[s];return r.filter((e,r,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===r).join(" ").trim()},c=e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,l.forwardRef)((e,r)=>{let{color:s="currentColor",size:a=24,strokeWidth:t=2,absoluteStrokeWidth:n,className:o="",children:x,iconNode:m,...h}=e;return(0,l.createElement)("svg",{ref:r,...d,width:a,height:a,stroke:s,strokeWidth:n?24*Number(t)/Number(a):t,className:i("lucide",o),...!x&&!c(h)&&{"aria-hidden":"true"},...h},[...m.map(e=>{let[r,s]=e;return(0,l.createElement)(r,s)}),...Array.isArray(x)?x:[x]])}),x=(e,r)=>{let s=(0,l.forwardRef)((s,t)=>{let{className:c,...d}=s;return(0,l.createElement)(o,{ref:t,iconNode:r,className:i("lucide-".concat(a(n(e))),"lucide-".concat(e),c),...d})});return s.displayName=n(e),s}}},e=>{var r=r=>e(e.s=r);e.O(0,[874,441,684,358],()=>r(9203)),_N_E=e.O()}]);