(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{2269:(e,t,i)=>{"use strict";var n=i(9509);i(8375);var s=i(2115),r=function(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}(s),o=void 0!==n&&n.env&&!0,c=function(e){return"[object String]"===Object.prototype.toString.call(e)},l=function(){function e(e){var t=void 0===e?{}:e,i=t.name,n=void 0===i?"stylesheet":i,s=t.optimizeForSpeed,r=void 0===s?o:s;d(c(n),"`name` must be a string"),this._name=n,this._deletedRulePlaceholder="#"+n+"-deleted-rule____{}",d("boolean"==typeof r,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=r,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0;var l="undefined"!=typeof window&&document.querySelector('meta[property="csp-nonce"]');this._nonce=l?l.getAttribute("content"):null}var t,i=e.prototype;return i.setOptimizeForSpeed=function(e){d("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),d(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},i.isOptimizeForSpeed=function(){return this._optimizeForSpeed},i.inject=function(){var e=this;if(d(!this._injected,"sheet already injected"),this._injected=!0,"undefined"!=typeof window&&this._optimizeForSpeed){this._tags[0]=this.makeStyleTag(this._name),this._optimizeForSpeed="insertRule"in this.getSheet(),this._optimizeForSpeed||(o||console.warn("StyleSheet: optimizeForSpeed mode not supported falling back to standard mode."),this.flush(),this._injected=!0);return}this._serverSheet={cssRules:[],insertRule:function(t,i){return"number"==typeof i?e._serverSheet.cssRules[i]={cssText:t}:e._serverSheet.cssRules.push({cssText:t}),i},deleteRule:function(t){e._serverSheet.cssRules[t]=null}}},i.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]},i.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},i.insertRule=function(e,t){if(d(c(e),"`insertRule` accepts only strings"),"undefined"==typeof window)return"number"!=typeof t&&(t=this._serverSheet.cssRules.length),this._serverSheet.insertRule(e,t),this._rulesCount++;if(this._optimizeForSpeed){var i=this.getSheet();"number"!=typeof t&&(t=i.cssRules.length);try{i.insertRule(e,t)}catch(t){return o||console.warn("StyleSheet: illegal rule: \n\n"+e+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),-1}}else{var n=this._tags[t];this._tags.push(this.makeStyleTag(this._name,e,n))}return this._rulesCount++},i.replaceRule=function(e,t){if(this._optimizeForSpeed||"undefined"==typeof window){var i="undefined"!=typeof window?this.getSheet():this._serverSheet;if(t.trim()||(t=this._deletedRulePlaceholder),!i.cssRules[e])return e;i.deleteRule(e);try{i.insertRule(t,e)}catch(n){o||console.warn("StyleSheet: illegal rule: \n\n"+t+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),i.insertRule(this._deletedRulePlaceholder,e)}}else{var n=this._tags[e];d(n,"old rule at index `"+e+"` not found"),n.textContent=t}return e},i.deleteRule=function(e){if("undefined"==typeof window)return void this._serverSheet.deleteRule(e);if(this._optimizeForSpeed)this.replaceRule(e,"");else{var t=this._tags[e];d(t,"rule at index `"+e+"` not found"),t.parentNode.removeChild(t),this._tags[e]=null}},i.flush=function(){this._injected=!1,this._rulesCount=0,"undefined"!=typeof window?(this._tags.forEach(function(e){return e&&e.parentNode.removeChild(e)}),this._tags=[]):this._serverSheet.cssRules=[]},i.cssRules=function(){var e=this;return"undefined"==typeof window?this._serverSheet.cssRules:this._tags.reduce(function(t,i){return i?t=t.concat(Array.prototype.map.call(e.getSheetForTag(i).cssRules,function(t){return t.cssText===e._deletedRulePlaceholder?null:t})):t.push(null),t},[])},i.makeStyleTag=function(e,t,i){t&&d(c(t),"makeStyleTag accepts only strings as second parameter");var n=document.createElement("style");this._nonce&&n.setAttribute("nonce",this._nonce),n.type="text/css",n.setAttribute("data-"+e,""),t&&n.appendChild(document.createTextNode(t));var s=document.head||document.getElementsByTagName("head")[0];return i?s.insertBefore(n,i):s.appendChild(n),n},t=[{key:"length",get:function(){return this._rulesCount}}],function(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}(e.prototype,t),e}();function d(e,t){if(!e)throw Error("StyleSheet: "+t+".")}var a=function(e){for(var t=5381,i=e.length;i;)t=33*t^e.charCodeAt(--i);return t>>>0},u={};function h(e,t){if(!t)return"jsx-"+e;var i=String(t),n=e+i;return u[n]||(u[n]="jsx-"+a(e+"-"+i)),u[n]}function m(e,t){"undefined"==typeof window&&(t=t.replace(/\/style/gi,"\\/style"));var i=e+t;return u[i]||(u[i]=t.replace(/__jsx-style-dynamic-selector/g,e)),u[i]}var f=function(){function e(e){var t=void 0===e?{}:e,i=t.styleSheet,n=void 0===i?null:i,s=t.optimizeForSpeed,r=void 0!==s&&s;this._sheet=n||new l({name:"styled-jsx",optimizeForSpeed:r}),this._sheet.inject(),n&&"boolean"==typeof r&&(this._sheet.setOptimizeForSpeed(r),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer=void 0,this._indices={},this._instancesCounts={}}var t=e.prototype;return t.add=function(e){var t=this;void 0===this._optimizeForSpeed&&(this._optimizeForSpeed=Array.isArray(e.children),this._sheet.setOptimizeForSpeed(this._optimizeForSpeed),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),"undefined"==typeof window||this._fromServer||(this._fromServer=this.selectFromServer(),this._instancesCounts=Object.keys(this._fromServer).reduce(function(e,t){return e[t]=0,e},{}));var i=this.getIdAndRules(e),n=i.styleId,s=i.rules;if(n in this._instancesCounts){this._instancesCounts[n]+=1;return}var r=s.map(function(e){return t._sheet.insertRule(e)}).filter(function(e){return -1!==e});this._indices[n]=r,this._instancesCounts[n]=1},t.remove=function(e){var t=this,i=this.getIdAndRules(e).styleId;if(function(e,t){if(!e)throw Error("StyleSheetRegistry: "+t+".")}(i in this._instancesCounts,"styleId: `"+i+"` not found"),this._instancesCounts[i]-=1,this._instancesCounts[i]<1){var n=this._fromServer&&this._fromServer[i];n?(n.parentNode.removeChild(n),delete this._fromServer[i]):(this._indices[i].forEach(function(e){return t._sheet.deleteRule(e)}),delete this._indices[i]),delete this._instancesCounts[i]}},t.update=function(e,t){this.add(t),this.remove(e)},t.flush=function(){this._sheet.flush(),this._sheet.inject(),this._fromServer=void 0,this._indices={},this._instancesCounts={}},t.cssRules=function(){var e=this,t=this._fromServer?Object.keys(this._fromServer).map(function(t){return[t,e._fromServer[t]]}):[],i=this._sheet.cssRules();return t.concat(Object.keys(this._indices).map(function(t){return[t,e._indices[t].map(function(e){return i[e].cssText}).join(e._optimizeForSpeed?"":"\n")]}).filter(function(e){return!!e[1]}))},t.styles=function(e){var t,i;return t=this.cssRules(),void 0===(i=e)&&(i={}),t.map(function(e){var t=e[0],n=e[1];return r.default.createElement("style",{id:"__"+t,key:"__"+t,nonce:i.nonce?i.nonce:void 0,dangerouslySetInnerHTML:{__html:n}})})},t.getIdAndRules=function(e){var t=e.children,i=e.dynamic,n=e.id;if(i){var s=h(n,i);return{styleId:s,rules:Array.isArray(t)?t.map(function(e){return m(s,e)}):[m(s,t)]}}return{styleId:h(n),rules:Array.isArray(t)?t:[t]}},t.selectFromServer=function(){return Array.prototype.slice.call(document.querySelectorAll('[id^="__jsx-"]')).reduce(function(e,t){return e[t.id.slice(2)]=t,e},{})},e}(),p=s.createContext(null);p.displayName="StyleSheetContext";var _=r.default.useInsertionEffect||r.default.useLayoutEffect,y="undefined"!=typeof window?new f:void 0;function x(e){var t=y||s.useContext(p);return t&&("undefined"==typeof window?t.add(e):_(function(){return t.add(e),function(){t.remove(e)}},[e.id,String(e.dynamic)])),null}x.dynamic=function(e){return e.map(function(e){return h(e[0],e[1])}).join(" ")},t.style=x},3325:(e,t,i)=>{"use strict";i.d(t,{default:()=>o});var n=i(5155),s=i(9137),r=i.n(s);function o(e){let{children:t,className:i=""}=e;return(0,n.jsxs)("div",{className:"jsx-5f89f7c83c8c1909 "+"mobile-optimized ".concat(i),children:[t,(0,n.jsx)(r(),{id:"5f89f7c83c8c1909",children:".mobile-optimized.jsx-5f89f7c83c8c1909{-webkit-tap-highlight-color:transparent;-webkit-touch-callout:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.mobile-optimized.jsx-5f89f7c83c8c1909 *.jsx-5f89f7c83c8c1909{-webkit-overflow-scrolling:touch;scroll-behavior:smooth}@media(max-width:768px){.mobile-optimized.jsx-5f89f7c83c8c1909{font-size:16px;line-height:1.5}.mobile-optimized.jsx-5f89f7c83c8c1909 button.jsx-5f89f7c83c8c1909,.mobile-optimized.jsx-5f89f7c83c8c1909 a.jsx-5f89f7c83c8c1909{min-height:44px;min-width:44px;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center}.mobile-optimized.jsx-5f89f7c83c8c1909 input.jsx-5f89f7c83c8c1909,.mobile-optimized.jsx-5f89f7c83c8c1909 textarea.jsx-5f89f7c83c8c1909{font-size:16px;-webkit-border-radius:8px;-moz-border-radius:8px;border-radius:8px;padding:12px}}@media(min-width:768px)and (max-width:1024px){.mobile-optimized.jsx-5f89f7c83c8c1909{font-size:15px}}"})]})}},4936:(e,t,i)=>{Promise.resolve().then(i.t.bind(i,6874,23)),Promise.resolve().then(i.bind(i,3325)),Promise.resolve().then(i.bind(i,4994))},4994:(e,t,i)=>{"use strict";i.d(t,{default:()=>o});var n=i(5155),s=i(9137),r=i.n(s);function o(e){let{children:t,className:i="",cols:s={mobile:2,tablet:3,desktop:4},gap:o={mobile:"1rem",tablet:"1.5rem",desktop:"2rem"}}=e,c=["grid-cols-".concat(s.mobile||2),"md:grid-cols-".concat(s.tablet||3),"lg:grid-cols-".concat(s.desktop||4),"gap-4 md:gap-6 lg:gap-8"].join(" ");return(0,n.jsxs)("div",{className:r().dynamic([["ad63202d5d04dfe5",[o.mobile||"1rem",o.tablet||"1.5rem",o.desktop||"2rem"]]])+" "+"grid ".concat(c," ").concat(i),children:[t,(0,n.jsx)(r(),{id:"ad63202d5d04dfe5",dynamic:[o.mobile||"1rem",o.tablet||"1.5rem",o.desktop||"2rem"],children:".grid.__jsx-style-dynamic-selector>*.__jsx-style-dynamic-selector{height:100%;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column}@media(max-width:768px){.grid.__jsx-style-dynamic-selector{gap:".concat(o.mobile||"1rem","}.grid.__jsx-style-dynamic-selector>*.__jsx-style-dynamic-selector{min-height:160px}}@media(min-width:768px)and (max-width:1024px){.grid.__jsx-style-dynamic-selector{gap:").concat(o.tablet||"1.5rem","}.grid.__jsx-style-dynamic-selector>*.__jsx-style-dynamic-selector{min-height:180px}}@media(min-width:1024px){.grid.__jsx-style-dynamic-selector{gap:").concat(o.desktop||"2rem","}.grid.__jsx-style-dynamic-selector>*.__jsx-style-dynamic-selector{min-height:200px}}@media(min-width:1920px){.grid.__jsx-style-dynamic-selector{max-width:1600px;margin:0 auto}.grid.__jsx-style-dynamic-selector>*.__jsx-style-dynamic-selector{min-height:220px}}")})]})}},8375:()=>{},9137:(e,t,i)=>{"use strict";e.exports=i(2269).style}},e=>{var t=t=>e(e.s=t);e.O(0,[874,441,684,358],()=>t(4936)),_N_E=e.O()}]);