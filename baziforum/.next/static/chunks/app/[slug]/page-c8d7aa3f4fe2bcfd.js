(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[182],{1366:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("message-circle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},1976:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},2657:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3619:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6874,23)),Promise.resolve().then(s.bind(s,9482))},9434:(e,t,s)=>{"use strict";function a(e){let t=new Date(e),s=Math.floor((new Date().getTime()-t.getTime())/1e3);if(s<60)return"刚刚";if(s<3600){let e=Math.floor(s/60);return"".concat(e,"分钟前")}if(s<86400){let e=Math.floor(s/3600);return"".concat(e,"小时前")}{if(!(s<2592e3))return t.toLocaleDateString("zh-CN");let e=Math.floor(s/86400);return"".concat(e,"天前")}}function r(e){return e<1e3?e.toString():e<1e4?"".concat((e/1e3).toFixed(1),"k"):"".concat((e/1e4).toFixed(1),"w")}s.d(t,{Yq:()=>a,ZV:()=>r})},9482:(e,t,s)=>{"use strict";s.d(t,{default:()=>p});var a=s(5155),r=s(2115),l=s(6874),c=s.n(l),n=s(9434),i=s(9946);let o=(0,i.A)("pin",[["path",{d:"M12 17v5",key:"bb1du9"}],["path",{d:"M9 10.76a2 2 0 0 1-1.11 1.79l-1.78.9A2 2 0 0 0 5 15.24V16a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-.76a2 2 0 0 0-1.11-1.79l-1.78-.9A2 2 0 0 1 15 10.76V7a1 1 0 0 1 1-1 2 2 0 0 0 0-4H8a2 2 0 0 0 0 4 1 1 0 0 1 1 1z",key:"1nkz8b"}]]);var d=s(1976),x=s(1366),m=s(2657);function h(e){let{post:t}=e;return(0,a.jsxs)("div",{className:"card hover:shadow-md transition-shadow",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat("新"===t.category?"bg-red-100 text-red-700":"闻"===t.category?"bg-blue-100 text-blue-700":"自"===t.category?"bg-green-100 text-green-700":"由"===t.category?"bg-yellow-100 text-yellow-700":"舆"===t.category?"bg-purple-100 text-purple-700":"论"===t.category?"bg-indigo-100 text-indigo-700":"监"===t.category?"bg-orange-100 text-orange-700":"bg-gray-100 text-gray-700"),children:t.category}),t.isSticky&&(0,a.jsx)(o,{className:"w-4 h-4 text-orange-500"}),t.isHot&&(0,a.jsx)("span",{className:"text-red-500 text-xs",children:"\uD83D\uDD25"})]}),(0,a.jsx)("span",{className:"text-xs text-gray-500",children:(0,n.Yq)(t.createdAt)})]}),(0,a.jsxs)(c(),{href:"/post/".concat(t.id),className:"block group",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 group-hover:text-blue-600 transition-colors mb-2 line-clamp-2",children:t.title}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-3 line-clamp-2",children:t.excerpt})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-500",children:[(0,a.jsx)("div",{className:"flex items-center space-x-1",children:(0,a.jsx)("span",{className:"font-medium",children:t.author})}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(d.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:(0,n.ZV)(t.likes)})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(x.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:(0,n.ZV)(t.comments)})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(m.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:(0,n.ZV)(t.views)})]})]})]}),t.tags.length>0&&(0,a.jsx)("div",{className:"flex flex-wrap gap-1 mt-3",children:t.tags.slice(0,3).map(e=>(0,a.jsxs)("span",{className:"px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded",children:["#",e]},e))})]})}let g=(0,i.A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);function p(e){let{section:t,posts:s}=e,[l,n]=(0,r.useState)("hot"),i=[...s].sort((e,t)=>"hot"===l?e.isSticky&&!t.isSticky?-1:!e.isSticky&&t.isSticky?1:t.likes+t.comments+t.views-(e.likes+e.comments+e.views):"latest"===l?new Date(t.createdAt).getTime()-new Date(e.createdAt).getTime():0),o=[{key:"hot",label:"热门",count:s.length},{key:"latest",label:"最新",count:s.length},{key:"mine",label:"我的",count:0}];return(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 mb-6",children:(0,a.jsx)("div",{className:"flex border-b border-gray-200",children:o.map(e=>(0,a.jsxs)("button",{onClick:()=>n(e.key),className:"flex-1 px-6 py-4 text-sm font-medium transition-colors ".concat(l===e.key?"text-blue-600 border-b-2 border-blue-600 bg-blue-50":"text-gray-500 hover:text-gray-700 hover:bg-gray-50"),children:[e.label,(0,a.jsx)("span",{className:"ml-2 px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded-full",children:e.count})]},e.key))})}),(0,a.jsx)("div",{className:"space-y-4",children:"mine"===l?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"text-gray-400 text-lg mb-2",children:"暂无内容"}),(0,a.jsx)("p",{className:"text-gray-500 text-sm",children:"登录后可查看您发布的帖子"})]}):i.length>0?i.map(e=>(0,a.jsx)(h,{post:e},e.id)):(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"text-gray-400 text-lg mb-2",children:"暂无帖子"}),(0,a.jsx)("p",{className:"text-gray-500 text-sm",children:"成为第一个在此板块发帖的人吧！"}),(0,a.jsxs)(c(),{href:"/".concat(t.slug,"/new"),className:"btn-primary mt-4 inline-flex items-center space-x-2",children:[(0,a.jsx)(g,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"发布帖子"})]})]})})]})}},9946:(e,t,s)=>{"use strict";s.d(t,{A:()=>x});var a=s(2115);let r=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase()),c=e=>{let t=l(e);return t.charAt(0).toUpperCase()+t.slice(1)},n=function(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return t.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim()},i=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,a.forwardRef)((e,t)=>{let{color:s="currentColor",size:r=24,strokeWidth:l=2,absoluteStrokeWidth:c,className:d="",children:x,iconNode:m,...h}=e;return(0,a.createElement)("svg",{ref:t,...o,width:r,height:r,stroke:s,strokeWidth:c?24*Number(l)/Number(r):l,className:n("lucide",d),...!x&&!i(h)&&{"aria-hidden":"true"},...h},[...m.map(e=>{let[t,s]=e;return(0,a.createElement)(t,s)}),...Array.isArray(x)?x:[x]])}),x=(e,t)=>{let s=(0,a.forwardRef)((s,l)=>{let{className:i,...o}=s;return(0,a.createElement)(d,{ref:l,iconNode:t,className:n("lucide-".concat(r(c(e))),"lucide-".concat(e),i),...o})});return s.displayName=c(e),s}}},e=>{var t=t=>e(e.s=t);e.O(0,[874,441,684,358],()=>t(3619)),_N_E=e.O()}]);