(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[959],{2657:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},5477:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6874,23)),Promise.resolve().then(s.bind(s,7521))},7521:(e,t,s)=>{"use strict";s.d(t,{default:()=>i});var r=s(5155),a=s(2115),l=s(2657);let n=(0,s(9946).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]);function i(e){let{section:t}=e,[s,i]=(0,a.useState)(""),[d,c]=(0,a.useState)(""),[x,m]=(0,a.useState)(""),[o,u]=(0,a.useState)(!1),[h,p]=(0,a.useState)(!1),g=async e=>{e.preventDefault(),s.trim()&&d.trim()&&(p(!0),await new Promise(e=>setTimeout(e,1e3)),alert("发帖功能暂未开放，这是一个演示版本"),p(!1))},b=x.split(",").map(e=>e.trim()).filter(e=>e);return(0,r.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsx)("div",{className:"flex justify-end mb-4",children:(0,r.jsxs)("button",{type:"button",onClick:()=>u(!o),className:"btn-secondary flex items-center space-x-2",children:[(0,r.jsx)(l.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:o?"编辑":"预览"})]})}),(0,r.jsx)("form",{onSubmit:g,children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[o?(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,r.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat("新"===t.key?"bg-red-100 text-red-700":"闻"===t.key?"bg-blue-100 text-blue-700":"自"===t.key?"bg-green-100 text-green-700":"由"===t.key?"bg-yellow-100 text-yellow-700":"舆"===t.key?"bg-purple-100 text-purple-700":"论"===t.key?"bg-indigo-100 text-indigo-700":"监"===t.key?"bg-orange-100 text-orange-700":"bg-gray-100 text-gray-700"),children:t.key}),(0,r.jsx)("span",{className:"text-sm text-gray-500",children:"刚刚"})]}),(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:s||"标题预览"}),(0,r.jsx)("div",{className:"text-sm text-gray-500 mb-4",children:"作者：您的用户名"})]}),(0,r.jsx)("div",{className:"prose max-w-none mb-6",children:(0,r.jsx)("div",{className:"text-gray-700 leading-relaxed whitespace-pre-line",children:d||"内容预览"})}),b.length>0&&(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:b.map((e,t)=>(0,r.jsxs)("span",{className:"px-3 py-1 text-sm bg-gray-100 text-gray-600 rounded-full",children:["#",e]},t))})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"标题 *"}),(0,r.jsx)("input",{type:"text",required:!0,value:s,onChange:e=>i(e.target.value),className:"w-full px-3 py-2 text-lg border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请输入帖子标题...",maxLength:100}),(0,r.jsxs)("div",{className:"text-right text-sm text-gray-500 mt-1",children:[s.length,"/100"]})]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"内容 *"}),(0,r.jsx)("textarea",{required:!0,value:d,onChange:e=>c(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none",rows:12,placeholder:"请输入帖子内容..."}),(0,r.jsxs)("div",{className:"text-right text-sm text-gray-500 mt-1",children:[d.length," 字符"]})]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"标签 (可选)"}),(0,r.jsx)("input",{type:"text",value:x,onChange:e=>m(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请输入标签，用逗号分隔，如：学术诚信,高校,举报"}),b.length>0&&(0,r.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:b.map((e,t)=>(0,r.jsxs)("span",{className:"px-2 py-1 text-sm bg-blue-100 text-blue-700 rounded",children:["#",e]},t))})]})]}),(0,r.jsx)("div",{className:"flex justify-end pt-6 border-t border-gray-200",children:(0,r.jsxs)("button",{type:"submit",disabled:!s.trim()||!d.trim()||h,className:"btn-primary flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,r.jsx)(n,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:h?"发布中...":"发布帖子"})]})})]})}),(0,r.jsxs)("div",{className:"mt-6 bg-blue-50 rounded-lg p-4",children:[(0,r.jsx)("h3",{className:"font-medium text-blue-900 mb-2",children:"发帖指南"}),(0,r.jsxs)("ul",{className:"text-sm text-blue-800 space-y-1",children:[(0,r.jsxs)("li",{children:["• 请确保内容与 ",t.name," 板块主题相关"]}),(0,r.jsx)("li",{children:"• 保持理性讨论，尊重不同观点"}),(0,r.jsx)("li",{children:"• 提供可靠的信息来源，避免传播未经证实的消息"}),(0,r.jsx)("li",{children:"• 使用恰当的标签帮助其他用户找到您的帖子"})]})]})]})}},9946:(e,t,s)=>{"use strict";s.d(t,{A:()=>m});var r=s(2115);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase()),n=e=>{let t=l(e);return t.charAt(0).toUpperCase()+t.slice(1)},i=function(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return t.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim()},d=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let x=(0,r.forwardRef)((e,t)=>{let{color:s="currentColor",size:a=24,strokeWidth:l=2,absoluteStrokeWidth:n,className:x="",children:m,iconNode:o,...u}=e;return(0,r.createElement)("svg",{ref:t,...c,width:a,height:a,stroke:s,strokeWidth:n?24*Number(l)/Number(a):l,className:i("lucide",x),...!m&&!d(u)&&{"aria-hidden":"true"},...u},[...o.map(e=>{let[t,s]=e;return(0,r.createElement)(t,s)}),...Array.isArray(m)?m:[m]])}),m=(e,t)=>{let s=(0,r.forwardRef)((s,l)=>{let{className:d,...c}=s;return(0,r.createElement)(x,{ref:l,iconNode:t,className:i("lucide-".concat(a(n(e))),"lucide-".concat(e),d),...c})});return s.displayName=n(e),s}}},e=>{var t=t=>e(e.s=t);e.O(0,[874,441,684,358],()=>t(5477)),_N_E=e.O()}]);