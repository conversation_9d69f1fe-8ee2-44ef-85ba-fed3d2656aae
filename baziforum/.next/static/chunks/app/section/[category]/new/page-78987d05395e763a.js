(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[194],{2657:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3247:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>m});var r=t(5155),a=t(2115),l=t(6874),n=t.n(l),i=t(5695),c=t(5566),d=t(7550),o=t(2657);let x=(0,t(9946).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]);function m(e){let{params:s}=e;(0,i.useRouter)();let[t,l]=(0,a.useState)(""),[m,u]=(0,a.useState)(""),[h,p]=(0,a.useState)(""),[g,b]=(0,a.useState)(!1),[y,f]=(0,a.useState)(!1),j=c.n.find(e=>e.key===s.category);j||(0,i.notFound)();let N=async e=>{e.preventDefault(),t.trim()&&m.trim()&&(f(!0),await new Promise(e=>setTimeout(e,1e3)),alert("发帖功能暂未开放，这是一个演示版本"),f(!1))},v=h.split(",").map(e=>e.trim()).filter(e=>e);return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)("header",{className:"bg-white border-b border-gray-200 sticky top-0 z-50",children:(0,r.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)(n(),{href:"/section/".concat(j.key),className:"flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors",children:[(0,r.jsx)(d.A,{className:"w-5 h-5"}),(0,r.jsx)("span",{children:"返回板块"})]}),(0,r.jsx)("div",{className:"h-6 w-px bg-gray-300"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"text-2xl",children:j.icon}),(0,r.jsxs)("span",{className:"font-medium text-gray-900",children:["发布到 ",j.name]})]})]}),(0,r.jsx)("div",{className:"flex items-center space-x-3",children:(0,r.jsxs)("button",{type:"button",onClick:()=>b(!g),className:"btn-secondary flex items-center space-x-2",children:[(0,r.jsx)(o.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:g?"编辑":"预览"})]})})]})})}),(0,r.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsx)("form",{onSubmit:N,children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[g?(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,r.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat("新"===j.key?"bg-red-100 text-red-700":"闻"===j.key?"bg-blue-100 text-blue-700":"自"===j.key?"bg-green-100 text-green-700":"由"===j.key?"bg-yellow-100 text-yellow-700":"舆"===j.key?"bg-purple-100 text-purple-700":"论"===j.key?"bg-indigo-100 text-indigo-700":"监"===j.key?"bg-orange-100 text-orange-700":"bg-gray-100 text-gray-700"),children:j.key}),(0,r.jsx)("span",{className:"text-sm text-gray-500",children:"刚刚"})]}),(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:t||"标题预览"}),(0,r.jsx)("div",{className:"text-sm text-gray-500 mb-4",children:"作者：您的用户名"})]}),(0,r.jsx)("div",{className:"prose max-w-none mb-6",children:(0,r.jsx)("div",{className:"text-gray-700 leading-relaxed whitespace-pre-line",children:m||"内容预览"})}),v.length>0&&(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:v.map((e,s)=>(0,r.jsxs)("span",{className:"px-3 py-1 text-sm bg-gray-100 text-gray-600 rounded-full",children:["#",e]},s))})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"标题 *"}),(0,r.jsx)("input",{type:"text",required:!0,value:t,onChange:e=>l(e.target.value),className:"w-full px-3 py-2 text-lg border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请输入帖子标题...",maxLength:100}),(0,r.jsxs)("div",{className:"text-right text-sm text-gray-500 mt-1",children:[t.length,"/100"]})]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"内容 *"}),(0,r.jsx)("textarea",{required:!0,value:m,onChange:e=>u(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none",rows:12,placeholder:"请输入帖子内容..."}),(0,r.jsxs)("div",{className:"text-right text-sm text-gray-500 mt-1",children:[m.length," 字符"]})]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"标签 (可选)"}),(0,r.jsx)("input",{type:"text",value:h,onChange:e=>p(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请输入标签，用逗号分隔，如：学术诚信,高校,举报"}),v.length>0&&(0,r.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:v.map((e,s)=>(0,r.jsxs)("span",{className:"px-2 py-1 text-sm bg-blue-100 text-blue-700 rounded",children:["#",e]},s))})]})]}),(0,r.jsx)("div",{className:"flex justify-end pt-6 border-t border-gray-200",children:(0,r.jsxs)("button",{type:"submit",disabled:!t.trim()||!m.trim()||y,className:"btn-primary flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,r.jsx)(x,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:y?"发布中...":"发布帖子"})]})})]})}),(0,r.jsxs)("div",{className:"mt-6 bg-blue-50 rounded-lg p-4",children:[(0,r.jsx)("h3",{className:"font-medium text-blue-900 mb-2",children:"发帖指南"}),(0,r.jsxs)("ul",{className:"text-sm text-blue-800 space-y-1",children:[(0,r.jsxs)("li",{children:["• 请确保内容与 ",j.name," 板块主题相关"]}),(0,r.jsx)("li",{children:"• 保持理性讨论，尊重不同观点"}),(0,r.jsx)("li",{children:"• 提供可靠的信息来源，避免传播未经证实的消息"}),(0,r.jsx)("li",{children:"• 使用恰当的标签帮助其他用户找到您的帖子"})]})]})]})]})}},5566:(e,s,t)=>{"use strict";t.d(s,{n:()=>r});let r=[{key:"新",name:"最新事件",description:"浏览当前社会热点内容，作为热榜入口",color:"bg-red-500",icon:"\uD83D\uDD25"},{key:"闻",name:"重大事件档案",description:'汇总关键事件、转折记录，构成"公共记忆"',color:"bg-blue-500",icon:"\uD83D\uDCF0"},{key:"自",name:"个人观点发表",description:"自由表达区，含日志、原创内容等",color:"bg-green-500",icon:"✍️"},{key:"由",name:"假新闻溯源",description:"提供谣言分析、信息溯源内容，引导辟谣意识",color:"bg-yellow-500",icon:"\uD83D\uDD0D"},{key:"舆",name:"媒体观察",description:"分析媒体偏向、舆论塑造机制，促思辨",color:"bg-purple-500",icon:"\uD83D\uDC41️"},{key:"论",name:"话题辩论场",description:"开设辩题，设立正反立场，引导参与讨论",color:"bg-indigo-500",icon:"\uD83D\uDCAC"},{key:"监",name:"举报与公审",description:"用户举报展示，管理员与用户共同评价",color:"bg-orange-500",icon:"⚖️"},{key:"督",name:"社区进展公告",description:"开发日志、任务列表、人员招募、制度建设",color:"bg-gray-500",icon:"\uD83D\uDCCB"}]},5695:(e,s,t)=>{"use strict";var r=t(8999);t.o(r,"notFound")&&t.d(s,{notFound:function(){return r.notFound}}),t.o(r,"useRouter")&&t.d(s,{useRouter:function(){return r.useRouter}})},7050:(e,s,t)=>{Promise.resolve().then(t.bind(t,3247))},7550:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},9946:(e,s,t)=>{"use strict";t.d(s,{A:()=>x});var r=t(2115);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,s,t)=>t?t.toUpperCase():s.toLowerCase()),n=e=>{let s=l(e);return s.charAt(0).toUpperCase()+s.slice(1)},i=function(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return s.filter((e,s,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===s).join(" ").trim()},c=e=>{for(let s in e)if(s.startsWith("aria-")||"role"===s||"title"===s)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,r.forwardRef)((e,s)=>{let{color:t="currentColor",size:a=24,strokeWidth:l=2,absoluteStrokeWidth:n,className:o="",children:x,iconNode:m,...u}=e;return(0,r.createElement)("svg",{ref:s,...d,width:a,height:a,stroke:t,strokeWidth:n?24*Number(l)/Number(a):l,className:i("lucide",o),...!x&&!c(u)&&{"aria-hidden":"true"},...u},[...m.map(e=>{let[s,t]=e;return(0,r.createElement)(s,t)}),...Array.isArray(x)?x:[x]])}),x=(e,s)=>{let t=(0,r.forwardRef)((t,l)=>{let{className:c,...d}=t;return(0,r.createElement)(o,{ref:l,iconNode:s,className:i("lucide-".concat(a(n(e))),"lucide-".concat(e),c),...d})});return t.displayName=n(e),t}}},e=>{var s=s=>e(e.s=s);e.O(0,[874,441,684,358],()=>s(7050)),_N_E=e.O()}]);