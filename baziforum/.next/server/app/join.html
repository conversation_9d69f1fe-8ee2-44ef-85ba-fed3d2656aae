<!DOCTYPE html><html lang="zh-CN"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/_next/static/media/569ce4b8f30dc480-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="preload" href="/_next/static/media/93f479601ee12b01-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="stylesheet" href="/_next/static/css/c29207ed639136c5.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-29ebadaebe2fcb3f.js"/><script src="/_next/static/chunks/4bd1b696-a6a68ffbe959fe28.js" async=""></script><script src="/_next/static/chunks/684-7330868c9ce4b992.js" async=""></script><script src="/_next/static/chunks/main-app-10f0dedc7d8f4017.js" async=""></script><script src="/_next/static/chunks/874-99c1a17b8114a98f.js" async=""></script><script src="/_next/static/chunks/app/page-fc7feddd285a94bc.js" async=""></script><script src="/_next/static/chunks/app/join/page-f6d95262265d6828.js" async=""></script><meta name="next-size-adjust" content=""/><title>八字论坛 - 新闻自由 舆论监督</title><meta name="description" content="面向大学生的公共议题内容型社交平台，以新闻自由、舆论监督为核心价值"/><meta name="keywords" content="新闻自由,舆论监督,大学生,公共议题,社交平台"/><link rel="icon" href="/icon.svg?e976668d334838b1" type="image/svg+xml" sizes="any"/><script>document.querySelectorAll('body link[rel="icon"], body link[rel="apple-touch-icon"]').forEach(el => document.head.appendChild(el))</script><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__variable_5cfdac __variable_9a8899 antialiased"><div class="min-h-screen bg-gray-50"><header class="bg-white border-b border-gray-200"><div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8"><div class="flex items-center h-16"><a class="flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors" href="/"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-left w-5 h-5" aria-hidden="true"><path d="m12 19-7-7 7-7"></path><path d="M19 12H5"></path></svg><span>返回首页</span></a></div></div></header><div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12"><div class="text-center mb-12"><h1 class="text-4xl font-bold text-gray-900 mb-4">加入我们</h1><p class="text-xl text-gray-600">一起推动新闻自由与舆论监督的发展</p></div><div class="grid lg:grid-cols-3 gap-8 mb-12"><div class="card cursor-pointer transition-all ring-2 ring-blue-500 bg-blue-50"><div class="text-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-code w-12 h-12 mx-auto mb-4 text-blue-600" aria-hidden="true"><path d="m16 18 6-6-6-6"></path><path d="m8 6-6 6 6 6"></path></svg><h3 class="text-lg font-semibold text-gray-900 mb-2">前端开发</h3><p class="text-gray-600 text-sm mb-4">React/Next.js方向，负责平台功能开发</p><div class="flex flex-wrap gap-1 justify-center"><span class="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded">React</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded">Next.js</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded">TypeScript</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded">Tailwind CSS</span></div></div></div><div class="card cursor-pointer transition-all hover:shadow-md"><div class="text-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-pen-tool w-12 h-12 mx-auto mb-4 text-blue-600" aria-hidden="true"><path d="M15.707 21.293a1 1 0 0 1-1.414 0l-1.586-1.586a1 1 0 0 1 0-1.414l5.586-5.586a1 1 0 0 1 1.414 0l1.586 1.586a1 1 0 0 1 0 1.414z"></path><path d="m18 13-1.375-6.874a1 1 0 0 0-.746-.776L3.235 2.028a1 1 0 0 0-1.207 1.207L5.35 15.879a1 1 0 0 0 .776.746L13 18"></path><path d="m2.3 2.3 7.286 7.286"></path><circle cx="11" cy="11" r="2"></circle></svg><h3 class="text-lg font-semibold text-gray-900 mb-2">内容策划</h3><p class="text-gray-600 text-sm mb-4">公共议题关注者，负责内容策划与写作</p><div class="flex flex-wrap gap-1 justify-center"><span class="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded">新闻写作</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded">议题分析</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded">内容策划</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded">社会观察</span></div></div></div><div class="card cursor-pointer transition-all hover:shadow-md"><div class="text-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-users w-12 h-12 mx-auto mb-4 text-blue-600" aria-hidden="true"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><path d="M16 3.128a4 4 0 0 1 0 7.744"></path><path d="M22 21v-2a4 4 0 0 0-3-3.87"></path><circle cx="9" cy="7" r="4"></circle></svg><h3 class="text-lg font-semibold text-gray-900 mb-2">社群志愿者</h3><p class="text-gray-600 text-sm mb-4">协助社区运营、用户支持等工作</p><div class="flex flex-wrap gap-1 justify-center"><span class="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded">社区运营</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded">用户支持</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded">活动策划</span><span class="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded">沟通协调</span></div></div></div></div><div class="bg-white rounded-lg shadow-sm border border-gray-200 p-8"><h2 class="text-2xl font-bold text-gray-900 mb-6">申请表单</h2><form class="space-y-6"><div class="grid md:grid-cols-2 gap-6"><div><label class="block text-sm font-medium text-gray-700 mb-2">姓名 *</label><input type="text" required="" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="请输入您的姓名" value=""/></div><div><label class="block text-sm font-medium text-gray-700 mb-2">邮箱 *</label><input type="email" required="" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="请输入您的邮箱" value=""/></div></div><div><label class="block text-sm font-medium text-gray-700 mb-2">相关技能</label><div class="flex flex-wrap gap-2"><button type="button" class="px-3 py-1 text-sm rounded-full transition-colors bg-gray-100 text-gray-600 border border-gray-300 hover:bg-gray-200">React</button><button type="button" class="px-3 py-1 text-sm rounded-full transition-colors bg-gray-100 text-gray-600 border border-gray-300 hover:bg-gray-200">Next.js</button><button type="button" class="px-3 py-1 text-sm rounded-full transition-colors bg-gray-100 text-gray-600 border border-gray-300 hover:bg-gray-200">TypeScript</button><button type="button" class="px-3 py-1 text-sm rounded-full transition-colors bg-gray-100 text-gray-600 border border-gray-300 hover:bg-gray-200">Tailwind CSS</button></div></div><div><label class="block text-sm font-medium text-gray-700 mb-2">相关经验</label><textarea class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" rows="3" placeholder="请简述您的相关经验（可选）"></textarea></div><div><label class="block text-sm font-medium text-gray-700 mb-2">申请理由 *</label><textarea required="" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" rows="4" placeholder="请告诉我们您为什么想要加入我们，以及您希望为项目贡献什么..."></textarea></div><div class="flex flex-col sm:flex-row gap-4"><button type="submit" class="btn-primary flex-1">提交申请</button><a href="https://forms.gle/example" target="_blank" rel="noopener noreferrer" class="btn-secondary flex-1 flex items-center justify-center space-x-2"><span>Google Forms</span><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link w-4 h-4" aria-hidden="true"><path d="M15 3h6v6"></path><path d="M10 14 21 3"></path><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path></svg></a></div></form></div></div></div><!--$--><!--/$--><!--$--><!--/$--><script src="/_next/static/chunks/webpack-29ebadaebe2fcb3f.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[7555,[],\"\"]\n3:I[1295,[],\"\"]\n4:I[6874,[\"874\",\"static/chunks/874-99c1a17b8114a98f.js\",\"974\",\"static/chunks/app/page-fc7feddd285a94bc.js\"],\"\"]\n5:I[894,[],\"ClientPageRoot\"]\n6:I[9414,[\"874\",\"static/chunks/874-99c1a17b8114a98f.js\",\"335\",\"static/chunks/app/join/page-f6d95262265d6828.js\"],\"default\"]\n9:I[9665,[],\"MetadataBoundary\"]\nb:I[9665,[],\"OutletBoundary\"]\ne:I[4911,[],\"AsyncMetadataOutlet\"]\n10:I[9665,[],\"ViewportBoundary\"]\n12:I[6614,[],\"\"]\n:HL[\"/_next/static/media/569ce4b8f30dc480-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n:HL[\"/_next/static/media/93f479601ee12b01-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n:HL[\"/_next/static/css/c29207ed639136c5.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"ajA0fObdPjkipa2ZLOkTq\",\"p\":\"\",\"c\":[\"\",\"join\",\"\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"join\",{\"children\":[\"__PAGE__\",{}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/c29207ed639136c5.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"zh-CN\",\"children\":[\"$\",\"body\",null,{\"className\":\"__variable_5cfdac __variable_9a8899 antialiased\",\"children\":[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L3\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"div\",null,{\"className\":\"min-h-screen bg-gray-50 flex items-center justify-center\",\"children\":[\"$\",\"div\",null,{\"className\":\"max-w-md mx-auto text-center\",\"children\":[[\"$\",\"div\",null,{\"className\":\"text-6xl font-bold text-gray-300 mb-4\",\"children\":\"404\"}],[\"$\",\"h1\",null,{\"className\":\"text-2xl font-bold text-gray-900 mb-2\",\"children\":\"页面未找到\"}],[\"$\",\"p\",null,{\"className\":\"text-gray-600 mb-8\",\"children\":\"抱歉，您访问的页面不存在或已被移除。\"}],[\"$\",\"div\",null,{\"className\":\"flex flex-col sm:flex-row gap-4 justify-center\",\"children\":[[\"$\",\"$L4\",null,{\"href\":\"/\",\"className\":\"btn-primary flex items-center justify-center space-x-2\",\"children\":[[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-house w-4 h-4\",\"aria-hidden\":\"true\",\"children\":[[\"$\",\"path\",\"5wwlr5\",{\"d\":\"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8\"}],[\"$\",\"path\",\"1d0kgt\",{\"d\":\"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z\"}],\"$undefined\"]}],[\"$\",\"span\",null,{\"children\":\"返回首页\"}]]}],[\"$\",\"$L4\",null,{\"href\":\"/forum\",\"className\":\"btn-secondary flex items-center justify-center space-x-2\",\"children\":[\"$\",\"span\",null,{\"children\":\"进入论坛\"}]}]]}]]}]}],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]}]]}],{\"children\":[\"join\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L3\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"$L5\",null,{\"Component\":\"$6\",\"searchParams\":{},\"params\":{},\"promises\":[\"$@7\",\"$@8\"]}],[\"$\",\"$L9\",null,{\"children\":\"$La\"}],null,[\"$\",\"$Lb\",null,{\"children\":[\"$Lc\",\"$Ld\",[\"$\",\"$Le\",null,{\"promise\":\"$@f\"}]]}]]}],{},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"YVq-nTt1Aok1aURqyxo_A\",{\"children\":[[\"$\",\"$L10\",null,{\"children\":\"$L11\"}],[\"$\",\"meta\",null,{\"name\":\"next-size-adjust\",\"content\":\"\"}]]}],null]}],false]],\"m\":\"$undefined\",\"G\":[\"$12\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"13:\"$Sreact.suspense\"\n14:I[4911,[],\"AsyncMetadata\"]\n7:{}\n8:{}\na:[\"$\",\"$13\",null,{\"fallback\":null,\"children\":[\"$\",\"$L14\",null,{\"promise\":\"$@15\"}]}]\n"])</script><script>self.__next_f.push([1,"d:null\n"])</script><script>self.__next_f.push([1,"11:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\nc:null\n"])</script><script>self.__next_f.push([1,"15:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"八字论坛 - 新闻自由 舆论监督\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"面向大学生的公共议题内容型社交平台，以新闻自由、舆论监督为核心价值\"}],[\"$\",\"meta\",\"2\",{\"name\":\"keywords\",\"content\":\"新闻自由,舆论监督,大学生,公共议题,社交平台\"}],[\"$\",\"link\",\"3\",{\"rel\":\"icon\",\"href\":\"/icon.svg?e976668d334838b1\",\"type\":\"image/svg+xml\",\"sizes\":\"any\"}]],\"error\":null,\"digest\":\"$undefined\"}\nf:{\"metadata\":\"$15:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"])</script></body></html>