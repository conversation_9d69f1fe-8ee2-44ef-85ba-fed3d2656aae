(()=>{var e={};e.id=492,e.ids=[492],e.modules={440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n});var s=t(1658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},1681:()=>{},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3269:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,5814,23))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3479:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>l});var s=t(5239),n=t(8088),i=t(8170),o=t.n(i),a=t(893),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);t.d(r,d);let l={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,4413)),"/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/app/not-found.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,4413)),"/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=[],m={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},3873:e=>{"use strict";e.exports=require("path")},4413:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(7413),n=t(4536),i=t.n(n),o=t(8898);function a(){return(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"max-w-md mx-auto text-center",children:[(0,s.jsx)("div",{className:"text-6xl font-bold text-gray-300 mb-4",children:"404"}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"页面未找到"}),(0,s.jsx)("p",{className:"text-gray-600 mb-8",children:"抱歉，您访问的页面不存在或已被移除。"}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,s.jsxs)(i(),{href:"/",className:"btn-primary flex items-center justify-center space-x-2",children:[(0,s.jsx)(o.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"返回首页"})]}),(0,s.jsx)(i(),{href:"/forum",className:"btn-secondary flex items-center justify-center space-x-2",children:(0,s.jsx)("span",{children:"进入论坛"})})]})]})})}},4431:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>l,metadata:()=>d});var s=t(7413),n=t(2202),i=t.n(n),o=t(4988),a=t.n(o);t(1135);let d={title:"八字论坛 - 新闻自由 舆论监督",description:"面向大学生的公共议题内容型社交平台，以新闻自由、舆论监督为核心价值",keywords:"新闻自由,舆论监督,大学生,公共议题,社交平台"};function l({children:e}){return(0,s.jsx)("html",{lang:"zh-CN",children:(0,s.jsx)("body",{className:`${i().variable} ${a().variable} antialiased`,children:e})})}},6487:()=>{},6821:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,4536,23))},8129:()=>{},8335:()=>{},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")},9636:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6346,23)),Promise.resolve().then(t.t.bind(t,7924,23)),Promise.resolve().then(t.t.bind(t,5656,23)),Promise.resolve().then(t.t.bind(t,99,23)),Promise.resolve().then(t.t.bind(t,8243,23)),Promise.resolve().then(t.t.bind(t,8827,23)),Promise.resolve().then(t.t.bind(t,2763,23)),Promise.resolve().then(t.t.bind(t,7173,23))},9780:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6444,23)),Promise.resolve().then(t.t.bind(t,6042,23)),Promise.resolve().then(t.t.bind(t,8170,23)),Promise.resolve().then(t.t.bind(t,9477,23)),Promise.resolve().then(t.t.bind(t,9345,23)),Promise.resolve().then(t.t.bind(t,2089,23)),Promise.resolve().then(t.t.bind(t,6577,23)),Promise.resolve().then(t.t.bind(t,1307,23))}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,30],()=>t(3479));module.exports=s})();