(()=>{var e={};e.id=335,e.ids=[335],e.modules={440:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>a});var t=s(1658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},1681:()=>{},2688:(e,r,s)=>{"use strict";s.d(r,{A:()=>m});var t=s(3210);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,s)=>s?s.toUpperCase():r.toLowerCase()),i=e=>{let r=l(e);return r.charAt(0).toUpperCase()+r.slice(1)},n=(...e)=>e.filter((e,r,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===r).join(" ").trim(),o=e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,t.forwardRef)(({color:e="currentColor",size:r=24,strokeWidth:s=2,absoluteStrokeWidth:a,className:l="",children:i,iconNode:c,...m},x)=>(0,t.createElement)("svg",{ref:x,...d,width:r,height:r,stroke:e,strokeWidth:a?24*Number(s)/Number(r):s,className:n("lucide",l),...!i&&!o(m)&&{"aria-hidden":"true"},...m},[...c.map(([e,r])=>(0,t.createElement)(e,r)),...Array.isArray(i)?i:[i]])),m=(e,r)=>{let s=(0,t.forwardRef)(({className:s,...l},o)=>(0,t.createElement)(c,{ref:o,iconNode:r,className:n(`lucide-${a(i(e))}`,`lucide-${e}`,s),...l}));return s.displayName=i(e),s}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3269:(e,r,s)=>{Promise.resolve().then(s.t.bind(s,5814,23))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4214:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>p});var t=s(687),a=s(3210),l=s(5814),i=s.n(l),n=s(2688);let o=(0,n.A)("code",[["path",{d:"m16 18 6-6-6-6",key:"eg8j8"}],["path",{d:"m8 6-6 6 6 6",key:"ppft3o"}]]),d=(0,n.A)("pen-tool",[["path",{d:"M15.707 21.293a1 1 0 0 1-1.414 0l-1.586-1.586a1 1 0 0 1 0-1.414l5.586-5.586a1 1 0 0 1 1.414 0l1.586 1.586a1 1 0 0 1 0 1.414z",key:"nt11vn"}],["path",{d:"m18 13-1.375-6.874a1 1 0 0 0-.746-.776L3.235 2.028a1 1 0 0 0-1.207 1.207L5.35 15.879a1 1 0 0 0 .776.746L13 18",key:"15qc1e"}],["path",{d:"m2.3 2.3 7.286 7.286",key:"1wuzzi"}],["circle",{cx:"11",cy:"11",r:"2",key:"xmgehs"}]]),c=(0,n.A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]);var m=s(8559);let x=(0,n.A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]);function p(){let[e,r]=(0,a.useState)({name:"",email:"",role:"developer",message:"",skills:[],experience:""}),[s,l]=(0,a.useState)(!1),n=[{key:"developer",title:"前端开发",description:"React/Next.js方向，负责平台功能开发",icon:o,skills:["React","Next.js","TypeScript","Tailwind CSS"]},{key:"content",title:"内容策划",description:"公共议题关注者，负责内容策划与写作",icon:d,skills:["新闻写作","议题分析","内容策划","社会观察"]},{key:"volunteer",title:"社群志愿者",description:"协助社区运营、用户支持等工作",icon:c,skills:["社区运营","用户支持","活动策划","沟通协调"]}],p=e=>{r(r=>({...r,skills:r.skills?.includes(e)?r.skills.filter(r=>r!==e):[...r.skills||[],e]}))};return s?(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"max-w-md mx-auto bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center",children:[(0,t.jsx)("div",{className:"text-green-500 text-6xl mb-4",children:"✓"}),(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"申请已提交"}),(0,t.jsx)("p",{className:"text-gray-600 mb-6",children:"感谢您的申请！我们会在3个工作日内通过邮件与您联系。"}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(i(),{href:"/",className:"btn-primary w-full",children:"返回首页"}),(0,t.jsx)(i(),{href:"/about",className:"btn-secondary w-full",children:"了解更多"})]})]})}):(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,t.jsx)("header",{className:"bg-white border-b border-gray-200",children:(0,t.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsx)("div",{className:"flex items-center h-16",children:(0,t.jsxs)(i(),{href:"/",className:"flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors",children:[(0,t.jsx)(m.A,{className:"w-5 h-5"}),(0,t.jsx)("span",{children:"返回首页"})]})})})}),(0,t.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,t.jsxs)("div",{className:"text-center mb-12",children:[(0,t.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"加入我们"}),(0,t.jsx)("p",{className:"text-xl text-gray-600",children:"一起推动新闻自由与舆论监督的发展"})]}),(0,t.jsx)("div",{className:"grid lg:grid-cols-3 gap-8 mb-12",children:n.map(s=>{let a=s.icon;return(0,t.jsx)("div",{className:`card cursor-pointer transition-all ${e.role===s.key?"ring-2 ring-blue-500 bg-blue-50":"hover:shadow-md"}`,onClick:()=>r(e=>({...e,role:s.key})),children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(a,{className:"w-12 h-12 mx-auto mb-4 text-blue-600"}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:s.title}),(0,t.jsx)("p",{className:"text-gray-600 text-sm mb-4",children:s.description}),(0,t.jsx)("div",{className:"flex flex-wrap gap-1 justify-center",children:s.skills.map(e=>(0,t.jsx)("span",{className:"px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded",children:e},e))})]})},s.key)})}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-8",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"申请表单"}),(0,t.jsxs)("form",{onSubmit:r=>{r.preventDefault(),console.log("Form submitted:",e),l(!0)},className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"姓名 *"}),(0,t.jsx)("input",{type:"text",required:!0,value:e.name,onChange:e=>r(r=>({...r,name:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请输入您的姓名"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"邮箱 *"}),(0,t.jsx)("input",{type:"email",required:!0,value:e.email,onChange:e=>r(r=>({...r,email:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请输入您的邮箱"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"相关技能"}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:n.find(r=>r.key===e.role)?.skills.map(r=>(0,t.jsx)("button",{type:"button",onClick:()=>p(r),className:`px-3 py-1 text-sm rounded-full transition-colors ${e.skills?.includes(r)?"bg-blue-100 text-blue-700 border border-blue-300":"bg-gray-100 text-gray-600 border border-gray-300 hover:bg-gray-200"}`,children:r},r))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"相关经验"}),(0,t.jsx)("textarea",{value:e.experience,onChange:e=>r(r=>({...r,experience:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",rows:3,placeholder:"请简述您的相关经验（可选）"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"申请理由 *"}),(0,t.jsx)("textarea",{required:!0,value:e.message,onChange:e=>r(r=>({...r,message:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",rows:4,placeholder:"请告诉我们您为什么想要加入我们，以及您希望为项目贡献什么..."})]}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,t.jsx)("button",{type:"submit",className:"btn-primary flex-1",children:"提交申请"}),(0,t.jsxs)("a",{href:"https://forms.gle/example",target:"_blank",rel:"noopener noreferrer",className:"btn-secondary flex-1 flex items-center justify-center space-x-2",children:[(0,t.jsx)("span",{children:"Google Forms"}),(0,t.jsx)(x,{className:"w-4 h-4"})]})]})]})]})]})]})}},4389:(e,r,s)=>{Promise.resolve().then(s.bind(s,8395))},4413:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>n});var t=s(7413),a=s(4536),l=s.n(a),i=s(8898);function n(){return(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"max-w-md mx-auto text-center",children:[(0,t.jsx)("div",{className:"text-6xl font-bold text-gray-300 mb-4",children:"404"}),(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"页面未找到"}),(0,t.jsx)("p",{className:"text-gray-600 mb-8",children:"抱歉，您访问的页面不存在或已被移除。"}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,t.jsxs)(l(),{href:"/",className:"btn-primary flex items-center justify-center space-x-2",children:[(0,t.jsx)(i.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:"返回首页"})]}),(0,t.jsx)(l(),{href:"/forum",className:"btn-secondary flex items-center justify-center space-x-2",children:(0,t.jsx)("span",{children:"进入论坛"})})]})]})})}},4431:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>d,metadata:()=>o});var t=s(7413),a=s(2202),l=s.n(a),i=s(4988),n=s.n(i);s(1135);let o={title:"八字论坛 - 新闻自由 舆论监督",description:"面向大学生的公共议题内容型社交平台，以新闻自由、舆论监督为核心价值",keywords:"新闻自由,舆论监督,大学生,公共议题,社交平台"};function d({children:e}){return(0,t.jsx)("html",{lang:"zh-CN",children:(0,t.jsx)("body",{className:`${l().variable} ${n().variable} antialiased`,children:e})})}},6201:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>d});var t=s(5239),a=s(8088),l=s(8170),i=s.n(l),n=s(893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);s.d(r,o);let d={children:["",{children:["join",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,8395)),"/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/app/join/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,4431)),"/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,4413)),"/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/app/join/page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/join/page",pathname:"/join",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},6821:(e,r,s)=>{Promise.resolve().then(s.t.bind(s,4536,23))},7541:(e,r,s)=>{Promise.resolve().then(s.bind(s,4214))},8129:()=>{},8395:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/app/join/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/app/join/page.tsx","default")},8559:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(2688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")},9636:(e,r,s)=>{Promise.resolve().then(s.t.bind(s,6346,23)),Promise.resolve().then(s.t.bind(s,7924,23)),Promise.resolve().then(s.t.bind(s,5656,23)),Promise.resolve().then(s.t.bind(s,99,23)),Promise.resolve().then(s.t.bind(s,8243,23)),Promise.resolve().then(s.t.bind(s,8827,23)),Promise.resolve().then(s.t.bind(s,2763,23)),Promise.resolve().then(s.t.bind(s,7173,23))},9780:(e,r,s)=>{Promise.resolve().then(s.t.bind(s,6444,23)),Promise.resolve().then(s.t.bind(s,6042,23)),Promise.resolve().then(s.t.bind(s,8170,23)),Promise.resolve().then(s.t.bind(s,9477,23)),Promise.resolve().then(s.t.bind(s,9345,23)),Promise.resolve().then(s.t.bind(s,2089,23)),Promise.resolve().then(s.t.bind(s,6577,23)),Promise.resolve().then(s.t.bind(s,1307,23))}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[447,30],()=>s(6201));module.exports=t})();