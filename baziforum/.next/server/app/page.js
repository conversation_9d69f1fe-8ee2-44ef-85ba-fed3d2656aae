(()=>{var e={};e.id=974,e.ids=[974],e.modules={325:(e,t,s)=>{"use strict";s.d(t,{A:()=>p});var r=s(7413),i=s(6373);let n=(0,i.A)("flame",[["path",{d:"M8.5 14.5A2.5 2.5 0 0 0 11 12c0-1.38-.5-2-1-3-1.072-2.143-.224-4.054 2-6 .5 2.5 2 4.9 4 6.5 2 1.6 3 3.5 3 5.5a7 7 0 1 1-14 0c0-1.153.433-2.294 1-3a2.5 2.5 0 0 0 2.5 2.5z",key:"96xj49"}]]),o=(0,i.A)("archive",[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8",key:"1s80jp"}],["path",{d:"M10 12h4",key:"a56b0p"}]]),a=(0,i.A)("pen-tool",[["path",{d:"M15.707 21.293a1 1 0 0 1-1.414 0l-1.586-1.586a1 1 0 0 1 0-1.414l5.586-5.586a1 1 0 0 1 1.414 0l1.586 1.586a1 1 0 0 1 0 1.414z",key:"nt11vn"}],["path",{d:"m18 13-1.375-6.874a1 1 0 0 0-.746-.776L3.235 2.028a1 1 0 0 0-1.207 1.207L5.35 15.879a1 1 0 0 0 .776.746L13 18",key:"15qc1e"}],["path",{d:"m2.3 2.3 7.286 7.286",key:"1wuzzi"}],["circle",{cx:"11",cy:"11",r:"2",key:"xmgehs"}]]),l=(0,i.A)("search-check",[["path",{d:"m8 11 2 2 4-4",key:"1sed1v"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);var c=s(3596),d=s(9529);let m=(0,i.A)("scale",[["path",{d:"m16 16 3-8 3 8c-.87.65-1.92 1-3 1s-2.13-.35-3-1Z",key:"7g6ntu"}],["path",{d:"m2 16 3-8 3 8c-.87.65-1.92 1-3 1s-2.13-.35-3-1Z",key:"ijws7r"}],["path",{d:"M7 21h10",key:"1b0cd5"}],["path",{d:"M12 3v18",key:"108xh3"}],["path",{d:"M3 7h2c2 0 5-1 7-2 2 1 5 2 7 2h2",key:"3gwbw2"}]]),h=(0,i.A)("clipboard-list",[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1",key:"tgr4d6"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2",key:"116196"}],["path",{d:"M12 11h4",key:"1jrz19"}],["path",{d:"M12 16h4",key:"n85exb"}],["path",{d:"M8 11h.01",key:"1dfujw"}],["path",{d:"M8 16h.01",key:"18s6g9"}]]),u={Flame:n,Archive:o,PenTool:a,SearchCheck:l,Eye:c.A,MessageSquare:d.A,Scale:m,ClipboardList:h};function p({name:e,className:t="w-6 h-6"}){let s=u[e];return s?(0,r.jsx)(s,{className:t}):(0,r.jsx)("div",{className:t})}},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},1204:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d});var r=s(7413),i=s(4536),n=s.n(i),o=s(3794),a=s(325),l=s(8393),c=s(1752);function d(){return(0,r.jsxs)(l.default,{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50",children:[(0,r.jsx)("header",{className:"bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex justify-between items-center h-12 md:h-14",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("h1",{className:"text-lg md:text-xl font-bold text-gradient",children:"八字论坛"}),(0,r.jsx)("span",{className:"text-xs md:text-sm text-gray-500 hidden sm:inline",children:"新闻自由 舆论监督"})]}),(0,r.jsxs)("nav",{className:"flex space-x-3 md:space-x-6",children:[(0,r.jsx)(n(),{href:"/about",className:"text-gray-600 hover:text-blue-600 transition-colors text-sm md:text-base",children:"项目理念"}),(0,r.jsx)(n(),{href:"/join",className:"btn-primary text-sm md:text-base py-1.5 px-3 md:py-2 md:px-4",children:"加入我们"})]})]})})}),(0,r.jsx)("section",{className:"py-8 md:py-12 px-4 text-center",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,r.jsx)("h2",{className:"text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-4 md:mb-6",children:"新闻自由 舆论监督"}),(0,r.jsx)("p",{className:"text-base md:text-lg text-gray-600 mb-6 md:mb-8 max-w-2xl mx-auto",children:"不仅关乎社会，也关乎你我。当信息无法自由传播，我们所知道的就不一定是真相。"}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 md:gap-4 justify-center",children:[(0,r.jsx)(n(),{href:"/forum",className:"btn-primary text-base md:text-lg px-6 md:px-8 py-2.5 md:py-3",children:"进入论坛"}),(0,r.jsx)(n(),{href:"/about",className:"btn-secondary text-base md:text-lg px-6 md:px-8 py-2.5 md:py-3",children:"了解更多"})]})]})}),(0,r.jsx)("section",{className:"py-8 md:py-12 px-4",children:(0,r.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,r.jsx)("h3",{className:"text-2xl md:text-3xl font-bold text-center mb-8 md:mb-10 text-gray-900",children:"八字论坛"}),(0,r.jsx)(c.default,{cols:{mobile:2,tablet:3,desktop:4},gap:{mobile:"1rem",tablet:"1.5rem",desktop:"2rem"},children:o.n.map(e=>(0,r.jsx)(n(),{href:`/${e.slug}`,className:"group h-full",children:(0,r.jsxs)("div",{className:"card hover:shadow-lg transition-all duration-300 group-hover:scale-105 text-center h-full flex flex-col justify-between",children:[(0,r.jsxs)("div",{className:"flex-1 flex flex-col justify-center",children:[(0,r.jsx)("div",{className:"flex justify-center mb-3 md:mb-4",children:(0,r.jsx)(a.A,{name:e.icon,className:"w-8 h-8 md:w-10 md:h-10 text-gray-600 group-hover:text-blue-600 transition-colors"})}),(0,r.jsx)("div",{className:"bazi-char mb-2",children:e.key}),(0,r.jsx)("h4",{className:"font-semibold text-gray-900 mb-2 text-sm md:text-base",children:e.name})]}),(0,r.jsx)("p",{className:"text-xs md:text-sm text-gray-600 line-clamp-2 mt-auto",children:e.description})]})},e.key))})]})}),(0,r.jsx)("footer",{className:"bg-gray-900 text-white py-8 md:py-10 px-4",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[(0,r.jsxs)("blockquote",{className:"text-sm md:text-base mb-4 md:mb-6 italic leading-relaxed",children:["“新闻自由，舆论监督——不仅关乎社会，也关乎你我。",(0,r.jsx)("br",{className:"hidden md:block"}),"当信息无法自由传播，我们所知道的就不一定是真相；",(0,r.jsx)("br",{className:"hidden md:block"}),"当公众无法表达质疑，错误就可能一再发生。",(0,r.jsx)("br",{className:"hidden md:block"}),"这些权利，是你我的日常保障。”"]}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 md:gap-4 justify-center mb-6 md:mb-8",children:[(0,r.jsx)(n(),{href:"/join",className:"btn-primary text-sm md:text-base py-2 px-4 md:py-2.5 md:px-6",children:"加入我们"}),(0,r.jsx)(n(),{href:"/freemedia",className:"btn-secondary bg-gray-700 hover:bg-gray-600 text-white text-sm md:text-base py-2 px-4 md:py-2.5 md:px-6",children:"了解更多"})]}),(0,r.jsx)("p",{className:"text-gray-400 text-xs md:text-sm",children:"\xa9 2024 八字论坛. 致力于推动新闻自由与舆论监督."})]})})]})}},1681:()=>{},1752:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/components/ResponsiveGrid.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/components/ResponsiveGrid.tsx","default")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3269:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,5814,23))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3596:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(6373).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3794:(e,t,s)=>{"use strict";s.d(t,{n:()=>r});let r=[{key:"新",slug:"latest",name:"最新事件",description:"浏览当前社会热点内容，作为热榜入口",color:"bg-red-500",icon:"Flame"},{key:"闻",slug:"archives",name:"重大事件档案",description:'汇总关键事件、转折记录，构成"公共记忆"',color:"bg-blue-500",icon:"Archive"},{key:"自",slug:"opinions",name:"个人观点发表",description:"自由表达区，含日志、原创内容等",color:"bg-green-500",icon:"PenTool"},{key:"由",slug:"factcheck",name:"假新闻溯源",description:"提供谣言分析、信息溯源内容，引导辟谣意识",color:"bg-yellow-500",icon:"SearchCheck"},{key:"舆",slug:"mediawatch",name:"媒体观察",description:"分析媒体偏向、舆论塑造机制，促思辨",color:"bg-purple-500",icon:"Eye"},{key:"论",slug:"debates",name:"话题辩论场",description:"开设辩题，设立正反立场，引导参与讨论",color:"bg-indigo-500",icon:"MessageSquare"},{key:"监",slug:"reports",name:"举报与公审",description:"用户举报展示，管理员与用户共同评价",color:"bg-orange-500",icon:"Scale"},{key:"督",slug:"community",name:"社区进展公告",description:"开发日志、任务列表、人员招募、制度建设",color:"bg-gray-500",icon:"ClipboardList"}]},3873:e=>{"use strict";e.exports=require("path")},3919:(e,t,s)=>{"use strict";s.d(t,{default:()=>o});var r=s(687),i=s(6180),n=s.n(i);function o({children:e,className:t=""}){return(0,r.jsxs)("div",{className:`jsx-5f89f7c83c8c1909 mobile-optimized ${t}`,children:[e,(0,r.jsx)(n(),{id:"5f89f7c83c8c1909",children:".mobile-optimized.jsx-5f89f7c83c8c1909{-webkit-tap-highlight-color:transparent;-webkit-touch-callout:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.mobile-optimized.jsx-5f89f7c83c8c1909 *.jsx-5f89f7c83c8c1909{-webkit-overflow-scrolling:touch;scroll-behavior:smooth}@media(max-width:768px){.mobile-optimized.jsx-5f89f7c83c8c1909{font-size:16px;line-height:1.5}.mobile-optimized.jsx-5f89f7c83c8c1909 button.jsx-5f89f7c83c8c1909,.mobile-optimized.jsx-5f89f7c83c8c1909 a.jsx-5f89f7c83c8c1909{min-height:44px;min-width:44px;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center}.mobile-optimized.jsx-5f89f7c83c8c1909 input.jsx-5f89f7c83c8c1909,.mobile-optimized.jsx-5f89f7c83c8c1909 textarea.jsx-5f89f7c83c8c1909{font-size:16px;-webkit-border-radius:8px;-moz-border-radius:8px;border-radius:8px;padding:12px}}@media(min-width:768px)and (max-width:1024px){.mobile-optimized.jsx-5f89f7c83c8c1909{font-size:15px}}"})]})}},4272:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,5814,23)),Promise.resolve().then(s.bind(s,3919)),Promise.resolve().then(s.bind(s,8426))},4413:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(7413),i=s(4536),n=s.n(i),o=s(8898);function a(){return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"max-w-md mx-auto text-center",children:[(0,r.jsx)("div",{className:"text-6xl font-bold text-gray-300 mb-4",children:"404"}),(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"页面未找到"}),(0,r.jsx)("p",{className:"text-gray-600 mb-8",children:"抱歉，您访问的页面不存在或已被移除。"}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,r.jsxs)(n(),{href:"/",className:"btn-primary flex items-center justify-center space-x-2",children:[(0,r.jsx)(o.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"返回首页"})]}),(0,r.jsx)(n(),{href:"/forum",className:"btn-secondary flex items-center justify-center space-x-2",children:(0,r.jsx)("span",{children:"进入论坛"})})]})]})})}},4431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c,metadata:()=>l});var r=s(7413),i=s(2202),n=s.n(i),o=s(4988),a=s.n(o);s(1135);let l={title:"八字论坛 - 新闻自由 舆论监督",description:"面向大学生的公共议题内容型社交平台，以新闻自由、舆论监督为核心价值",keywords:"新闻自由,舆论监督,大学生,公共议题,社交平台"};function c({children:e}){return(0,r.jsx)("html",{lang:"zh-CN",children:(0,r.jsx)("body",{className:`${n().variable} ${a().variable} antialiased`,children:e})})}},4544:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,4536,23)),Promise.resolve().then(s.bind(s,8393)),Promise.resolve().then(s.bind(s,1752))},5913:(e,t,s)=>{"use strict";s(6397);var r=s(3210),i=function(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}(r),n="undefined"!=typeof process&&process.env&&!0,o=function(e){return"[object String]"===Object.prototype.toString.call(e)},a=function(){function e(e){var t=void 0===e?{}:e,s=t.name,r=void 0===s?"stylesheet":s,i=t.optimizeForSpeed,a=void 0===i?n:i;l(o(r),"`name` must be a string"),this._name=r,this._deletedRulePlaceholder="#"+r+"-deleted-rule____{}",l("boolean"==typeof a,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=a,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0,this._nonce=null}var t,s=e.prototype;return s.setOptimizeForSpeed=function(e){l("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),l(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},s.isOptimizeForSpeed=function(){return this._optimizeForSpeed},s.inject=function(){var e=this;l(!this._injected,"sheet already injected"),this._injected=!0,this._serverSheet={cssRules:[],insertRule:function(t,s){return"number"==typeof s?e._serverSheet.cssRules[s]={cssText:t}:e._serverSheet.cssRules.push({cssText:t}),s},deleteRule:function(t){e._serverSheet.cssRules[t]=null}}},s.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]},s.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},s.insertRule=function(e,t){return l(o(e),"`insertRule` accepts only strings"),"number"!=typeof t&&(t=this._serverSheet.cssRules.length),this._serverSheet.insertRule(e,t),this._rulesCount++},s.replaceRule=function(e,t){this._optimizeForSpeed;var s=this._serverSheet;if(t.trim()||(t=this._deletedRulePlaceholder),!s.cssRules[e])return e;s.deleteRule(e);try{s.insertRule(t,e)}catch(r){n||console.warn("StyleSheet: illegal rule: \n\n"+t+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),s.insertRule(this._deletedRulePlaceholder,e)}return e},s.deleteRule=function(e){this._serverSheet.deleteRule(e)},s.flush=function(){this._injected=!1,this._rulesCount=0,this._serverSheet.cssRules=[]},s.cssRules=function(){return this._serverSheet.cssRules},s.makeStyleTag=function(e,t,s){t&&l(o(t),"makeStyleTag accepts only strings as second parameter");var r=document.createElement("style");this._nonce&&r.setAttribute("nonce",this._nonce),r.type="text/css",r.setAttribute("data-"+e,""),t&&r.appendChild(document.createTextNode(t));var i=document.head||document.getElementsByTagName("head")[0];return s?i.insertBefore(r,s):i.appendChild(r),r},t=[{key:"length",get:function(){return this._rulesCount}}],function(e,t){for(var s=0;s<t.length;s++){var r=t[s];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}(e.prototype,t),e}();function l(e,t){if(!e)throw Error("StyleSheet: "+t+".")}var c=function(e){for(var t=5381,s=e.length;s;)t=33*t^e.charCodeAt(--s);return t>>>0},d={};function m(e,t){if(!t)return"jsx-"+e;var s=String(t),r=e+s;return d[r]||(d[r]="jsx-"+c(e+"-"+s)),d[r]}function h(e,t){var s=e+(t=t.replace(/\/style/gi,"\\/style"));return d[s]||(d[s]=t.replace(/__jsx-style-dynamic-selector/g,e)),d[s]}var u=function(){function e(e){var t=void 0===e?{}:e,s=t.styleSheet,r=void 0===s?null:s,i=t.optimizeForSpeed,n=void 0!==i&&i;this._sheet=r||new a({name:"styled-jsx",optimizeForSpeed:n}),this._sheet.inject(),r&&"boolean"==typeof n&&(this._sheet.setOptimizeForSpeed(n),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer=void 0,this._indices={},this._instancesCounts={}}var t=e.prototype;return t.add=function(e){var t=this;void 0===this._optimizeForSpeed&&(this._optimizeForSpeed=Array.isArray(e.children),this._sheet.setOptimizeForSpeed(this._optimizeForSpeed),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed());var s=this.getIdAndRules(e),r=s.styleId,i=s.rules;if(r in this._instancesCounts){this._instancesCounts[r]+=1;return}var n=i.map(function(e){return t._sheet.insertRule(e)}).filter(function(e){return -1!==e});this._indices[r]=n,this._instancesCounts[r]=1},t.remove=function(e){var t=this,s=this.getIdAndRules(e).styleId;if(function(e,t){if(!e)throw Error("StyleSheetRegistry: "+t+".")}(s in this._instancesCounts,"styleId: `"+s+"` not found"),this._instancesCounts[s]-=1,this._instancesCounts[s]<1){var r=this._fromServer&&this._fromServer[s];r?(r.parentNode.removeChild(r),delete this._fromServer[s]):(this._indices[s].forEach(function(e){return t._sheet.deleteRule(e)}),delete this._indices[s]),delete this._instancesCounts[s]}},t.update=function(e,t){this.add(t),this.remove(e)},t.flush=function(){this._sheet.flush(),this._sheet.inject(),this._fromServer=void 0,this._indices={},this._instancesCounts={}},t.cssRules=function(){var e=this,t=this._fromServer?Object.keys(this._fromServer).map(function(t){return[t,e._fromServer[t]]}):[],s=this._sheet.cssRules();return t.concat(Object.keys(this._indices).map(function(t){return[t,e._indices[t].map(function(e){return s[e].cssText}).join(e._optimizeForSpeed?"":"\n")]}).filter(function(e){return!!e[1]}))},t.styles=function(e){var t,s;return t=this.cssRules(),void 0===(s=e)&&(s={}),t.map(function(e){var t=e[0],r=e[1];return i.default.createElement("style",{id:"__"+t,key:"__"+t,nonce:s.nonce?s.nonce:void 0,dangerouslySetInnerHTML:{__html:r}})})},t.getIdAndRules=function(e){var t=e.children,s=e.dynamic,r=e.id;if(s){var i=m(r,s);return{styleId:i,rules:Array.isArray(t)?t.map(function(e){return h(i,e)}):[h(i,t)]}}return{styleId:m(r),rules:Array.isArray(t)?t:[t]}},t.selectFromServer=function(){return Array.prototype.slice.call(document.querySelectorAll('[id^="__jsx-"]')).reduce(function(e,t){return e[t.id.slice(2)]=t,e},{})},e}(),p=r.createContext(null);p.displayName="StyleSheetContext";i.default.useInsertionEffect||i.default.useLayoutEffect;var x=void 0;function f(e){var t=x||r.useContext(p);return t&&t.add(e),null}f.dynamic=function(e){return e.map(function(e){return m(e[0],e[1])}).join(" ")},t.style=f},6180:(e,t,s)=>{"use strict";e.exports=s(5913).style},6397:()=>{},6821:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,4536,23))},7557:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>h,tree:()=>c});var r=s(5239),i=s(8088),n=s(8170),o=s.n(n),a=s(893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);s.d(t,l);let c={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,1204)),"/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/app/page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,9645))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,4431)),"/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,4413)),"/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,9645))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/app/page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},8129:()=>{},8393:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/components/MobileOptimized.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/components/MobileOptimized.tsx","default")},8426:(e,t,s)=>{"use strict";s.d(t,{default:()=>o});var r=s(687),i=s(6180),n=s.n(i);function o({children:e,className:t="",cols:s={mobile:2,tablet:3,desktop:4},gap:i={mobile:"1rem",tablet:"1.5rem",desktop:"2rem"}}){let o=`grid-cols-${s.mobile||2} md:grid-cols-${s.tablet||3} lg:grid-cols-${s.desktop||4} gap-4 md:gap-6 lg:gap-8`;return(0,r.jsxs)("div",{className:n().dynamic([["ad63202d5d04dfe5",[i.mobile||"1rem",i.tablet||"1.5rem",i.desktop||"2rem"]]])+" "+`grid ${o} ${t}`,children:[e,(0,r.jsx)(n(),{id:"ad63202d5d04dfe5",dynamic:[i.mobile||"1rem",i.tablet||"1.5rem",i.desktop||"2rem"],children:`.grid.__jsx-style-dynamic-selector>*.__jsx-style-dynamic-selector{height:100%;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column}@media(max-width:768px){.grid.__jsx-style-dynamic-selector{gap:${i.mobile||"1rem"}}.grid.__jsx-style-dynamic-selector>*.__jsx-style-dynamic-selector{min-height:160px}}@media(min-width:768px)and (max-width:1024px){.grid.__jsx-style-dynamic-selector{gap:${i.tablet||"1.5rem"}}.grid.__jsx-style-dynamic-selector>*.__jsx-style-dynamic-selector{min-height:180px}}@media(min-width:1024px){.grid.__jsx-style-dynamic-selector{gap:${i.desktop||"2rem"}}.grid.__jsx-style-dynamic-selector>*.__jsx-style-dynamic-selector{min-height:200px}}@media(min-width:1920px){.grid.__jsx-style-dynamic-selector{max-width:1600px;margin:0 auto}.grid.__jsx-style-dynamic-selector>*.__jsx-style-dynamic-selector{min-height:220px}}`})]})}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9529:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(6373).A)("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},9551:e=>{"use strict";e.exports=require("url")},9636:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6346,23)),Promise.resolve().then(s.t.bind(s,7924,23)),Promise.resolve().then(s.t.bind(s,5656,23)),Promise.resolve().then(s.t.bind(s,99,23)),Promise.resolve().then(s.t.bind(s,8243,23)),Promise.resolve().then(s.t.bind(s,8827,23)),Promise.resolve().then(s.t.bind(s,2763,23)),Promise.resolve().then(s.t.bind(s,7173,23))},9645:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var r=s(1658);let i=async e=>[{type:"image/svg+xml",sizes:"any",url:(0,r.fillMetadataSegment)(".",await e.params,"icon.svg")+"?e976668d334838b1"}]},9780:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6444,23)),Promise.resolve().then(s.t.bind(s,6042,23)),Promise.resolve().then(s.t.bind(s,8170,23)),Promise.resolve().then(s.t.bind(s,9477,23)),Promise.resolve().then(s.t.bind(s,9345,23)),Promise.resolve().then(s.t.bind(s,2089,23)),Promise.resolve().then(s.t.bind(s,6577,23)),Promise.resolve().then(s.t.bind(s,1307,23))}};var t=require("../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,30],()=>s(7557));module.exports=r})();