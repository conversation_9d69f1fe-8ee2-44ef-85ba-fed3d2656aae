(()=>{var e={};e.id=767,e.ids=[767],e.modules={372:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>v});var r=t(687),a=t(3210),i=t(5814),n=t.n(i),c=t(6189),o=t(2892),l=t(6973),d=t(4780),x=t(2688);let m=(0,x.A)("pin",[["path",{d:"M12 17v5",key:"bb1du9"}],["path",{d:"M9 10.76a2 2 0 0 1-1.11 1.79l-1.78.9A2 2 0 0 0 5 15.24V16a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-.76a2 2 0 0 0-1.11-1.79l-1.78-.9A2 2 0 0 1 15 10.76V7a1 1 0 0 1 1-1 2 2 0 0 0 0-4H8a2 2 0 0 0 0 4 1 1 0 0 1 1 1z",key:"1nkz8b"}]]);var p=t(7760),h=t(3872),g=t(3861);function u({post:e}){return(0,r.jsxs)("div",{className:"card hover:shadow-md transition-shadow",children:[(0,r.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${"新"===e.category?"bg-red-100 text-red-700":"闻"===e.category?"bg-blue-100 text-blue-700":"自"===e.category?"bg-green-100 text-green-700":"由"===e.category?"bg-yellow-100 text-yellow-700":"舆"===e.category?"bg-purple-100 text-purple-700":"论"===e.category?"bg-indigo-100 text-indigo-700":"监"===e.category?"bg-orange-100 text-orange-700":"bg-gray-100 text-gray-700"}`,children:e.category}),e.isSticky&&(0,r.jsx)(m,{className:"w-4 h-4 text-orange-500"}),e.isHot&&(0,r.jsx)("span",{className:"text-red-500 text-xs",children:"\uD83D\uDD25"})]}),(0,r.jsx)("span",{className:"text-xs text-gray-500",children:(0,d.Yq)(e.createdAt)})]}),(0,r.jsxs)(n(),{href:`/post/${e.id}`,className:"block group",children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-900 group-hover:text-blue-600 transition-colors mb-2 line-clamp-2",children:e.title}),(0,r.jsx)("p",{className:"text-gray-600 text-sm mb-3 line-clamp-2",children:e.excerpt})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-500",children:[(0,r.jsx)("div",{className:"flex items-center space-x-1",children:(0,r.jsx)("span",{className:"font-medium",children:e.author})}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(p.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:(0,d.ZV)(e.likes)})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(h.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:(0,d.ZV)(e.comments)})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(g.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:(0,d.ZV)(e.views)})]})]})]}),e.tags.length>0&&(0,r.jsx)("div",{className:"flex flex-wrap gap-1 mt-3",children:e.tags.slice(0,3).map(e=>(0,r.jsxs)("span",{className:"px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded",children:["#",e]},e))})]})}var y=t(8559);let b=(0,x.A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);function v({params:e}){let[s,t]=(0,a.useState)("hot"),i=o.n.find(s=>s.key===e.category);i||(0,c.notFound)();let d=l.G.filter(e=>e.category===i.key),x=[...d].sort((e,t)=>"hot"===s?e.isSticky&&!t.isSticky?-1:!e.isSticky&&t.isSticky?1:t.likes+t.comments+t.views-(e.likes+e.comments+e.views):"latest"===s?new Date(t.createdAt).getTime()-new Date(e.createdAt).getTime():0),m=[{key:"hot",label:"热门",count:d.length},{key:"latest",label:"最新",count:d.length},{key:"mine",label:"我的",count:0}];return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)("header",{className:"bg-white border-b border-gray-200 sticky top-0 z-50",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)(n(),{href:"/",className:"flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors",children:[(0,r.jsx)(y.A,{className:"w-5 h-5"}),(0,r.jsx)("span",{children:"返回首页"})]}),(0,r.jsx)("div",{className:"h-6 w-px bg-gray-300"}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"text-3xl",children:i.icon}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:i.name}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:i.description})]})]})]}),(0,r.jsxs)(n(),{href:`/section/${i.key}/new`,className:"btn-primary flex items-center space-x-2",children:[(0,r.jsx)(b,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"发帖"})]})]})})}),(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 mb-6",children:(0,r.jsx)("div",{className:"flex border-b border-gray-200",children:m.map(e=>(0,r.jsxs)("button",{onClick:()=>t(e.key),className:`flex-1 px-6 py-4 text-sm font-medium transition-colors ${s===e.key?"text-blue-600 border-b-2 border-blue-600 bg-blue-50":"text-gray-500 hover:text-gray-700 hover:bg-gray-50"}`,children:[e.label,(0,r.jsx)("span",{className:"ml-2 px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded-full",children:e.count})]},e.key))})}),(0,r.jsx)("div",{className:"space-y-4",children:"mine"===s?(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("div",{className:"text-gray-400 text-lg mb-2",children:"暂无内容"}),(0,r.jsx)("p",{className:"text-gray-500 text-sm",children:"登录后可查看您发布的帖子"})]}):x.length>0?x.map(e=>(0,r.jsx)(u,{post:e},e.id)):(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("div",{className:"text-gray-400 text-lg mb-2",children:"暂无帖子"}),(0,r.jsx)("p",{className:"text-gray-500 text-sm",children:"成为第一个在此板块发帖的人吧！"}),(0,r.jsxs)(n(),{href:`/section/${i.key}/new`,className:"btn-primary mt-4 inline-flex items-center space-x-2",children:[(0,r.jsx)(b,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"发布帖子"})]})]})})]})]})}},725:(e,s,t)=>{Promise.resolve().then(t.bind(t,4047))},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2892:(e,s,t)=>{"use strict";t.d(s,{n:()=>r});let r=[{key:"新",name:"最新事件",description:"浏览当前社会热点内容，作为热榜入口",color:"bg-red-500",icon:"\uD83D\uDD25"},{key:"闻",name:"重大事件档案",description:'汇总关键事件、转折记录，构成"公共记忆"',color:"bg-blue-500",icon:"\uD83D\uDCF0"},{key:"自",name:"个人观点发表",description:"自由表达区，含日志、原创内容等",color:"bg-green-500",icon:"✍️"},{key:"由",name:"假新闻溯源",description:"提供谣言分析、信息溯源内容，引导辟谣意识",color:"bg-yellow-500",icon:"\uD83D\uDD0D"},{key:"舆",name:"媒体观察",description:"分析媒体偏向、舆论塑造机制，促思辨",color:"bg-purple-500",icon:"\uD83D\uDC41️"},{key:"论",name:"话题辩论场",description:"开设辩题，设立正反立场，引导参与讨论",color:"bg-indigo-500",icon:"\uD83D\uDCAC"},{key:"监",name:"举报与公审",description:"用户举报展示，管理员与用户共同评价",color:"bg-orange-500",icon:"⚖️"},{key:"督",name:"社区进展公告",description:"开发日志、任务列表、人员招募、制度建设",color:"bg-gray-500",icon:"\uD83D\uDCCB"}]},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3273:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>d,routeModule:()=>m,tree:()=>l});var r=t(5239),a=t(8088),i=t(8170),n=t.n(i),c=t(893),o={};for(let e in c)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>c[e]);t.d(s,o);let l={children:["",{children:["section",{children:["[category]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,4047)),"/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/app/section/[category]/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,4413)),"/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/app/section/[category]/page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/section/[category]/page",pathname:"/section/[category]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4047:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/app/section/[category]/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/app/section/[category]/page.tsx","default")},4293:(e,s,t)=>{Promise.resolve().then(t.bind(t,372))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,30,7],()=>t(3273));module.exports=r})();