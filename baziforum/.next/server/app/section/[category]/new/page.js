(()=>{var e={};e.id=194,e.ids=[194],e.modules={440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(1658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1102:(e,t,r)=>{Promise.resolve().then(r.bind(r,4027))},1135:()=>{},1681:()=>{},2688:(e,t,r)=>{"use strict";r.d(t,{A:()=>m});var s=r(3210);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),n=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),i=e=>{let t=n(e);return t.charAt(0).toUpperCase()+t.slice(1)},l=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim(),o=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,s.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:a,className:n="",children:i,iconNode:c,...m},x)=>(0,s.createElement)("svg",{ref:x,...d,width:t,height:t,stroke:e,strokeWidth:a?24*Number(r)/Number(t):r,className:l("lucide",n),...!i&&!o(m)&&{"aria-hidden":"true"},...m},[...c.map(([e,t])=>(0,s.createElement)(e,t)),...Array.isArray(i)?i:[i]])),m=(e,t)=>{let r=(0,s.forwardRef)(({className:r,...n},o)=>(0,s.createElement)(c,{ref:o,iconNode:t,className:l(`lucide-${a(i(e))}`,`lucide-${e}`,r),...n}));return r.displayName=i(e),r}},2892:(e,t,r)=>{"use strict";r.d(t,{n:()=>s});let s=[{key:"新",name:"最新事件",description:"浏览当前社会热点内容，作为热榜入口",color:"bg-red-500",icon:"\uD83D\uDD25"},{key:"闻",name:"重大事件档案",description:'汇总关键事件、转折记录，构成"公共记忆"',color:"bg-blue-500",icon:"\uD83D\uDCF0"},{key:"自",name:"个人观点发表",description:"自由表达区，含日志、原创内容等",color:"bg-green-500",icon:"✍️"},{key:"由",name:"假新闻溯源",description:"提供谣言分析、信息溯源内容，引导辟谣意识",color:"bg-yellow-500",icon:"\uD83D\uDD0D"},{key:"舆",name:"媒体观察",description:"分析媒体偏向、舆论塑造机制，促思辨",color:"bg-purple-500",icon:"\uD83D\uDC41️"},{key:"论",name:"话题辩论场",description:"开设辩题，设立正反立场，引导参与讨论",color:"bg-indigo-500",icon:"\uD83D\uDCAC"},{key:"监",name:"举报与公审",description:"用户举报展示，管理员与用户共同评价",color:"bg-orange-500",icon:"⚖️"},{key:"督",name:"社区进展公告",description:"开发日志、任务列表、人员招募、制度建设",color:"bg-gray-500",icon:"\uD83D\uDCCB"}]},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3269:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,5814,23))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3861:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(2688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3873:e=>{"use strict";e.exports=require("path")},4027:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>x});var s=r(687),a=r(3210),n=r(5814),i=r.n(n),l=r(6189),o=r(2892),d=r(8559),c=r(3861);let m=(0,r(2688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]);function x({params:e}){(0,l.useRouter)();let[t,r]=(0,a.useState)(""),[n,x]=(0,a.useState)(""),[u,p]=(0,a.useState)(""),[h,b]=(0,a.useState)(!1),[g,y]=(0,a.useState)(!1),f=o.n.find(t=>t.key===e.category);f||(0,l.notFound)();let v=async e=>{e.preventDefault(),t.trim()&&n.trim()&&(y(!0),await new Promise(e=>setTimeout(e,1e3)),alert("发帖功能暂未开放，这是一个演示版本"),y(!1))},j=u.split(",").map(e=>e.trim()).filter(e=>e);return(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,s.jsx)("header",{className:"bg-white border-b border-gray-200 sticky top-0 z-50",children:(0,s.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)(i(),{href:`/section/${f.key}`,className:"flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors",children:[(0,s.jsx)(d.A,{className:"w-5 h-5"}),(0,s.jsx)("span",{children:"返回板块"})]}),(0,s.jsx)("div",{className:"h-6 w-px bg-gray-300"}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("div",{className:"text-2xl",children:f.icon}),(0,s.jsxs)("span",{className:"font-medium text-gray-900",children:["发布到 ",f.name]})]})]}),(0,s.jsx)("div",{className:"flex items-center space-x-3",children:(0,s.jsxs)("button",{type:"button",onClick:()=>b(!h),className:"btn-secondary flex items-center space-x-2",children:[(0,s.jsx)(c.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:h?"编辑":"预览"})]})})]})})}),(0,s.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,s.jsx)("form",{onSubmit:v,children:(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[h?(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,s.jsx)("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${"新"===f.key?"bg-red-100 text-red-700":"闻"===f.key?"bg-blue-100 text-blue-700":"自"===f.key?"bg-green-100 text-green-700":"由"===f.key?"bg-yellow-100 text-yellow-700":"舆"===f.key?"bg-purple-100 text-purple-700":"论"===f.key?"bg-indigo-100 text-indigo-700":"监"===f.key?"bg-orange-100 text-orange-700":"bg-gray-100 text-gray-700"}`,children:f.key}),(0,s.jsx)("span",{className:"text-sm text-gray-500",children:"刚刚"})]}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:t||"标题预览"}),(0,s.jsx)("div",{className:"text-sm text-gray-500 mb-4",children:"作者：您的用户名"})]}),(0,s.jsx)("div",{className:"prose max-w-none mb-6",children:(0,s.jsx)("div",{className:"text-gray-700 leading-relaxed whitespace-pre-line",children:n||"内容预览"})}),j.length>0&&(0,s.jsx)("div",{className:"flex flex-wrap gap-2",children:j.map((e,t)=>(0,s.jsxs)("span",{className:"px-3 py-1 text-sm bg-gray-100 text-gray-600 rounded-full",children:["#",e]},t))})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"标题 *"}),(0,s.jsx)("input",{type:"text",required:!0,value:t,onChange:e=>r(e.target.value),className:"w-full px-3 py-2 text-lg border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请输入帖子标题...",maxLength:100}),(0,s.jsxs)("div",{className:"text-right text-sm text-gray-500 mt-1",children:[t.length,"/100"]})]}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"内容 *"}),(0,s.jsx)("textarea",{required:!0,value:n,onChange:e=>x(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none",rows:12,placeholder:"请输入帖子内容..."}),(0,s.jsxs)("div",{className:"text-right text-sm text-gray-500 mt-1",children:[n.length," 字符"]})]}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"标签 (可选)"}),(0,s.jsx)("input",{type:"text",value:u,onChange:e=>p(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请输入标签，用逗号分隔，如：学术诚信,高校,举报"}),j.length>0&&(0,s.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:j.map((e,t)=>(0,s.jsxs)("span",{className:"px-2 py-1 text-sm bg-blue-100 text-blue-700 rounded",children:["#",e]},t))})]})]}),(0,s.jsx)("div",{className:"flex justify-end pt-6 border-t border-gray-200",children:(0,s.jsxs)("button",{type:"submit",disabled:!t.trim()||!n.trim()||g,className:"btn-primary flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,s.jsx)(m,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:g?"发布中...":"发布帖子"})]})})]})}),(0,s.jsxs)("div",{className:"mt-6 bg-blue-50 rounded-lg p-4",children:[(0,s.jsx)("h3",{className:"font-medium text-blue-900 mb-2",children:"发帖指南"}),(0,s.jsxs)("ul",{className:"text-sm text-blue-800 space-y-1",children:[(0,s.jsxs)("li",{children:["• 请确保内容与 ",f.name," 板块主题相关"]}),(0,s.jsx)("li",{children:"• 保持理性讨论，尊重不同观点"}),(0,s.jsx)("li",{children:"• 提供可靠的信息来源，避免传播未经证实的消息"}),(0,s.jsx)("li",{children:"• 使用恰当的标签帮助其他用户找到您的帖子"})]})]})]})]})}},4413:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var s=r(7413),a=r(4536),n=r.n(a),i=r(8898);function l(){return(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"max-w-md mx-auto text-center",children:[(0,s.jsx)("div",{className:"text-6xl font-bold text-gray-300 mb-4",children:"404"}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"页面未找到"}),(0,s.jsx)("p",{className:"text-gray-600 mb-8",children:"抱歉，您访问的页面不存在或已被移除。"}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,s.jsxs)(n(),{href:"/",className:"btn-primary flex items-center justify-center space-x-2",children:[(0,s.jsx)(i.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"返回首页"})]}),(0,s.jsx)(n(),{href:"/forum",className:"btn-secondary flex items-center justify-center space-x-2",children:(0,s.jsx)("span",{children:"进入论坛"})})]})]})})}},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,metadata:()=>o});var s=r(7413),a=r(2202),n=r.n(a),i=r(4988),l=r.n(i);r(1135);let o={title:"八字论坛 - 新闻自由 舆论监督",description:"面向大学生的公共议题内容型社交平台，以新闻自由、舆论监督为核心价值",keywords:"新闻自由,舆论监督,大学生,公共议题,社交平台"};function d({children:e}){return(0,s.jsx)("html",{lang:"zh-CN",children:(0,s.jsx)("body",{className:`${n().variable} ${l().variable} antialiased`,children:e})})}},5830:(e,t,r)=>{Promise.resolve().then(r.bind(r,8158))},6189:(e,t,r)=>{"use strict";var s=r(5773);r.o(s,"notFound")&&r.d(t,{notFound:function(){return s.notFound}}),r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}})},6249:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>d});var s=r(5239),a=r(8088),n=r(8170),i=r.n(n),l=r(893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(t,o);let d={children:["",{children:["section",{children:["[category]",{children:["new",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,8158)),"/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/app/section/[category]/new/page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,4413)),"/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/app/section/[category]/new/page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/section/[category]/new/page",pathname:"/section/[category]/new",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},6821:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,4536,23))},8129:()=>{},8158:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/app/section/[category]/new/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/app/section/[category]/new/page.tsx","default")},8559:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(2688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")},9636:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},9780:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,30],()=>r(6249));module.exports=s})();