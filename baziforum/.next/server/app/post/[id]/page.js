(()=>{var e={};e.id=457,e.ids=[457],e.modules={323:(e,t,s)=>{"use strict";s.d(t,{default:()=>c});var r=s(687),a=s(3210),n=s(4780),i=s(3861),o=s(7760),l=s(3872);function c({post:e,comments:t}){let[s,c]=(0,a.useState)(!1),[d,x]=(0,a.useState)(e.likes),[m,h]=(0,a.useState)("");return(0,r.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("article",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("span",{className:`px-3 py-1 text-sm font-medium rounded-full ${"新"===e.category?"bg-red-100 text-red-700":"闻"===e.category?"bg-blue-100 text-blue-700":"自"===e.category?"bg-green-100 text-green-700":"由"===e.category?"bg-yellow-100 text-yellow-700":"舆"===e.category?"bg-purple-100 text-purple-700":"论"===e.category?"bg-indigo-100 text-indigo-700":"监"===e.category?"bg-orange-100 text-orange-700":"bg-gray-100 text-gray-700"}`,children:e.category}),e.isHot&&(0,r.jsx)("span",{className:"text-red-500",children:"\uD83D\uDD25"})]}),(0,r.jsx)("span",{className:"text-sm text-gray-500",children:(0,n.Yq)(e.createdAt)})]}),(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:e.title}),(0,r.jsxs)("div",{className:"flex items-center space-x-6 text-sm text-gray-500 mb-6",children:[(0,r.jsx)("span",{className:"font-medium",children:e.author}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(i.A,{className:"w-4 h-4"}),(0,r.jsxs)("span",{children:[(0,n.ZV)(e.views)," 阅读"]})]})]}),(0,r.jsx)("div",{className:"prose max-w-none mb-6",children:(0,r.jsx)("p",{className:"text-gray-700 leading-relaxed whitespace-pre-line",children:e.content})}),e.tags.length>0&&(0,r.jsx)("div",{className:"flex flex-wrap gap-2 mb-6",children:e.tags.map(e=>(0,r.jsxs)("span",{className:"px-3 py-1 text-sm bg-gray-100 text-gray-600 rounded-full",children:["#",e]},e))}),(0,r.jsx)("div",{className:"flex items-center justify-between pt-4 border-t border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,r.jsxs)("button",{onClick:()=>{c(!s),x(e=>s?e-1:e+1)},className:`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${s?"bg-red-50 text-red-600 hover:bg-red-100":"text-gray-500 hover:bg-gray-50 hover:text-red-600"}`,children:[(0,r.jsx)(o.A,{className:`w-5 h-5 ${s?"fill-current":""}`}),(0,r.jsx)("span",{children:(0,n.ZV)(d)})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-gray-500",children:[(0,r.jsx)(l.A,{className:"w-5 h-5"}),(0,r.jsx)("span",{children:(0,n.ZV)(e.comments)})]})]})})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsxs)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:["评论 (",t.length,")"]}),(0,r.jsxs)("form",{onSubmit:e=>{e.preventDefault(),m.trim()&&(alert("评论功能暂未开放，这是一个演示版本"),h(""))},className:"mb-6",children:[(0,r.jsx)("textarea",{value:m,onChange:e=>h(e.target.value),placeholder:"写下你的想法...",className:"w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",rows:3}),(0,r.jsx)("div",{className:"flex justify-end mt-3",children:(0,r.jsx)("button",{type:"submit",disabled:!m.trim(),className:"btn-primary disabled:opacity-50 disabled:cursor-not-allowed",children:"发表评论"})})]}),(0,r.jsx)("div",{className:"space-y-4",children:t.length>0?t.map(e=>(0,r.jsxs)("div",{className:"border-b border-gray-100 pb-4 last:border-b-0",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,r.jsx)("span",{className:"font-medium text-gray-900",children:e.author}),(0,r.jsx)("span",{className:"text-sm text-gray-500",children:(0,n.Yq)(e.createdAt)})]}),(0,r.jsx)("p",{className:"text-gray-700 mb-2",children:e.content}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-500",children:[(0,r.jsxs)("button",{className:"hover:text-red-600 transition-colors",children:[(0,r.jsx)(o.A,{className:"w-4 h-4 inline mr-1"}),e.likes]}),(0,r.jsx)("button",{className:"hover:text-blue-600 transition-colors",children:"回复"})]})]},e.id)):(0,r.jsx)("div",{className:"text-center py-8 text-gray-500",children:"暂无评论，来发表第一条评论吧！"})})]})]})}},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3239:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,4536,23)),Promise.resolve().then(s.bind(s,3765)),Promise.resolve().then(s.bind(s,5845))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3765:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/components/PostContent.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/components/PostContent.tsx","default")},3872:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(2688).A)("message-circle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},3873:e=>{"use strict";e.exports=require("path")},3903:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>p,generateStaticParams:()=>u});var r=s(7413),a=s(4536),n=s.n(a),i=s(9916),o=s(9799);let l=[{id:"c1",postId:"1",author:"关注者A",content:"这种学术不端行为必须严厉打击，为这位勇敢举报的学生点赞！",createdAt:"2024-01-15T11:00:00Z",likes:23},{id:"c2",postId:"1",author:"理性思考者",content:"希望能够公正调查，既要保护举报人，也要确保被举报人的合法权益。",createdAt:"2024-01-15T11:30:00Z",likes:15},{id:"c3",postId:"4",author:"同龄人",content:"说得很好！我们这一代确实需要更多地关注这些看似遥远但实际很重要的议题。",createdAt:"2024-01-13T21:00:00Z",likes:8},{id:"c4",postId:"5",author:"媒体工作者",content:"非常实用的指南，建议每个人都应该掌握这些基本的信息验证技能。",createdAt:"2024-01-12T15:00:00Z",likes:12},{id:"c5",postId:"7",author:"支持实名制",content:"我支持网络实名制，这样可以让网络环境更加文明，减少恶意言论。",createdAt:"2024-01-09T12:00:00Z",likes:6},{id:"c6",postId:"7",author:"担心隐私",content:"但是实名制可能会让一些人不敢说真话，特别是涉及敏感话题时。",createdAt:"2024-01-09T12:30:00Z",likes:9},{id:"c7",postId:"10",author:"法律学生",content:"政府信息公开是公民的基本权利，希望这个案例能推动相关制度的完善。",createdAt:"2024-01-16T15:00:00Z",likes:12},{id:"c8",postId:"12",author:"媒体从业者",content:"互联网确实改变了新闻业，但也带来了新的挑战，比如信息过载和真假难辨。",createdAt:"2024-01-14T12:00:00Z",likes:8},{id:"c9",postId:"14",author:"教育工作者",content:"媒体素养教育应该从中学就开始，这是数字时代的基本技能。",createdAt:"2024-01-16T20:00:00Z",likes:15},{id:"c10",postId:"16",author:"科普爱好者",content:"谣言传播的速度总是比辟谣快，我们需要更有效的辟谣机制。",createdAt:"2024-01-16T14:00:00Z",likes:7},{id:"c11",postId:"19",author:"技术从业者",content:"AI在新闻领域的应用确实需要谨慎，特别是在事实核查和伦理方面。",createdAt:"2024-01-15T16:00:00Z",likes:11},{id:"c12",postId:"20",author:"平台用户",content:"我认为平台和用户都有责任，关键是要找到平衡点。",createdAt:"2024-01-16T16:00:00Z",likes:6},{id:"c13",postId:"22",author:"新闻伦理关注者",content:"收费删帖严重违背了新闻伦理，必须严厉打击这种行为。",createdAt:"2024-01-16T13:00:00Z",likes:18},{id:"c14",postId:"24",author:"活跃用户",content:"支持更新用户规范，这有助于维护社区的良好氛围。",createdAt:"2024-01-16T10:00:00Z",likes:5}];var c=s(3794),d=s(3765),x=s(5845),m=s(1465);let h=(0,s(6373).A)("share-2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]]);async function u(){return o.G.map(e=>({id:e.id}))}async function p({params:e}){let{id:t}=await e,s=o.G.find(e=>e.id===t);s||(0,i.notFound)();let a=l.filter(e=>e.postId===s.id),u=c.n.find(e=>e.key===s.category);return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50 flex",children:[(0,r.jsx)(x.default,{currentSlug:u?.slug}),(0,r.jsxs)("div",{className:"flex-1 flex flex-col",children:[(0,r.jsx)("header",{className:"bg-white border-b border-gray-200 sticky top-0 z-40",children:(0,r.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,r.jsx)("div",{className:"md:ml-0 ml-16",children:(0,r.jsxs)(n(),{href:u?`/${u.slug}`:"/forum",className:"flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors",children:[(0,r.jsx)(m.A,{className:"w-5 h-5"}),(0,r.jsx)("span",{className:"hidden sm:inline",children:"返回板块"})]})}),(0,r.jsx)("div",{className:"flex items-center space-x-4",children:(0,r.jsx)("button",{className:"p-2 text-gray-400 hover:text-gray-600 transition-colors",children:(0,r.jsx)(h,{className:"w-5 h-5"})})})]})})}),(0,r.jsx)(d.default,{post:s,comments:a})]})]})}},4780:(e,t,s)=>{"use strict";function r(e){let t=new Date(e),s=Math.floor((new Date().getTime()-t.getTime())/1e3);if(s<60)return"刚刚";if(s<3600){let e=Math.floor(s/60);return`${e}分钟前`}if(s<86400){let e=Math.floor(s/3600);return`${e}小时前`}{if(!(s<2592e3))return t.toLocaleDateString("zh-CN");let e=Math.floor(s/86400);return`${e}天前`}}function a(e){return e<1e3?e.toString():e<1e4?`${(e/1e3).toFixed(1)}k`:`${(e/1e4).toFixed(1)}w`}s.d(t,{Yq:()=>r,ZV:()=>a})},5087:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,5814,23)),Promise.resolve().then(s.bind(s,323)),Promise.resolve().then(s.bind(s,8375))},5845:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/components/SideNavigation.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/components/SideNavigation.tsx","default")},6217:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>d,routeModule:()=>m,tree:()=>c});var r=s(5239),a=s(8088),n=s(8170),i=s.n(n),o=s(893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let c={children:["",{children:["post",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,3903)),"/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/app/post/[id]/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,4431)),"/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,4413)),"/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/app/post/[id]/page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/post/[id]/page",pathname:"/post/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},7760:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(2688).A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},8375:(e,t,s)=>{"use strict";s.d(t,{default:()=>w});var r=s(687),a=s(3210),n=s(5814),i=s.n(n),o=s(6189);let l=[{key:"新",slug:"latest",name:"最新事件",description:"浏览当前社会热点内容，作为热榜入口",color:"bg-red-500",icon:"Flame"},{key:"闻",slug:"archives",name:"重大事件档案",description:'汇总关键事件、转折记录，构成"公共记忆"',color:"bg-blue-500",icon:"Archive"},{key:"自",slug:"opinions",name:"个人观点发表",description:"自由表达区，含日志、原创内容等",color:"bg-green-500",icon:"PenTool"},{key:"由",slug:"factcheck",name:"假新闻溯源",description:"提供谣言分析、信息溯源内容，引导辟谣意识",color:"bg-yellow-500",icon:"SearchCheck"},{key:"舆",slug:"mediawatch",name:"媒体观察",description:"分析媒体偏向、舆论塑造机制，促思辨",color:"bg-purple-500",icon:"Eye"},{key:"论",slug:"debates",name:"话题辩论场",description:"开设辩题，设立正反立场，引导参与讨论",color:"bg-indigo-500",icon:"MessageSquare"},{key:"监",slug:"reports",name:"举报与公审",description:"用户举报展示，管理员与用户共同评价",color:"bg-orange-500",icon:"Scale"},{key:"督",slug:"community",name:"社区进展公告",description:"开发日志、任务列表、人员招募、制度建设",color:"bg-gray-500",icon:"ClipboardList"}];var c=s(1923),d=s(7026),x=s(4606),m=s(4151),h=s(3861),u=s(8887),p=s(382),g=s(6727);let b={Flame:c.A,Archive:d.A,PenTool:x.A,SearchCheck:m.A,Eye:h.A,MessageSquare:u.A,Scale:p.A,ClipboardList:g.A};function f({name:e,className:t="w-6 h-6"}){let s=b[e];return s?(0,r.jsx)(s,{className:t}):(0,r.jsx)("div",{className:t})}var y=s(1860),v=s(2941),j=s(7033),N=s(4952);function w({currentSlug:e}){let[t,s]=(0,a.useState)(!1),[n,c]=(0,a.useState)(!1),d=(0,o.usePathname)(),x=()=>s(!t),m=()=>c(!n),h=()=>(0,r.jsxs)("div",{className:"flex flex-col h-full",children:[(0,r.jsx)("div",{className:"p-4 border-b border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(t||n)&&(0,r.jsx)("h2",{className:"font-semibold text-gray-900",children:"板块导航"}),(0,r.jsx)("button",{onClick:t?x:m,className:"p-1 rounded-lg hover:bg-gray-100 transition-colors md:hidden",children:n?(0,r.jsx)(y.A,{className:"w-5 h-5"}):(0,r.jsx)(v.A,{className:"w-5 h-5"})})]})}),(0,r.jsx)("nav",{className:"flex-1 p-4 space-y-2",children:l.map(s=>{let a=e===s.slug||d===`/${s.slug}`;return(0,r.jsxs)(i(),{href:`/${s.slug}`,className:`
                flex items-center space-x-3 p-3 rounded-lg transition-all duration-200
                ${a?"bg-blue-50 text-blue-600 border border-blue-200":"text-gray-700 hover:bg-gray-50 hover:text-blue-600"}
                ${!t&&!n?"justify-center":""}
              `,title:t||n?void 0:s.name,children:[(0,r.jsx)(f,{name:s.icon,className:`w-5 h-5 flex-shrink-0 ${a?"text-blue-600":""}`}),(t||n)&&(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("div",{className:"font-medium text-sm",children:s.name}),(0,r.jsx)("div",{className:"text-xs text-gray-500 truncate",children:s.description})]})]},s.key)})}),(0,r.jsx)("div",{className:"p-4 border-t border-gray-200",children:(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)(i(),{href:"/forum",className:`
              flex items-center space-x-3 p-2 rounded-lg transition-colors
              text-gray-600 hover:bg-gray-50 hover:text-blue-600
              ${!t&&!n?"justify-center":""}
            `,title:t||n?void 0:"论坛总览",children:[(0,r.jsx)(f,{name:"Grid3X3",className:"w-4 h-4"}),(t||n)&&(0,r.jsx)("span",{className:"text-sm",children:"论坛总览"})]}),(0,r.jsxs)(i(),{href:"/",className:`
              flex items-center space-x-3 p-2 rounded-lg transition-colors
              text-gray-600 hover:bg-gray-50 hover:text-blue-600
              ${!t&&!n?"justify-center":""}
            `,title:t||n?void 0:"返回首页",children:[(0,r.jsx)(f,{name:"Home",className:"w-4 h-4"}),(t||n)&&(0,r.jsx)("span",{className:"text-sm",children:"返回首页"})]})]})})]});return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:`
        hidden md:flex flex-col bg-white border-r border-gray-200 transition-all duration-300 relative
        ${t?"w-80":"w-16"}
      `,children:[(0,r.jsx)(h,{}),(0,r.jsx)("button",{onClick:x,className:"absolute top-4 -right-3 bg-white border border-gray-200 rounded-full p-1 shadow-sm hover:shadow-md transition-shadow z-10",children:t?(0,r.jsx)(j.A,{className:"w-4 h-4 text-gray-600"}):(0,r.jsx)(N.A,{className:"w-4 h-4 text-gray-600"})})]}),(0,r.jsx)("button",{onClick:m,className:"md:hidden fixed top-4 left-4 z-50 bg-white border border-gray-200 rounded-lg p-2 shadow-sm",children:(0,r.jsx)(v.A,{className:"w-5 h-5 text-gray-600"})}),n&&(0,r.jsxs)("div",{className:"md:hidden fixed inset-0 z-40",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50",onClick:m}),(0,r.jsx)("div",{className:"absolute left-0 top-0 h-full w-80 bg-white shadow-xl",children:(0,r.jsx)(h,{})})]})]})}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,30,177,247],()=>s(6217));module.exports=r})();