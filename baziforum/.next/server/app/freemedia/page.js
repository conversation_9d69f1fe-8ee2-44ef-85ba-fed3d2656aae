(()=>{var e={};e.id=683,e.ids=[683],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},1382:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(6373).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},1465:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(6373).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},1681:()=>{},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3269:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,5814,23))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4413:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>n});var t=r(7413),a=r(4536),l=r.n(a),i=r(8898);function n(){return(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"max-w-md mx-auto text-center",children:[(0,t.jsx)("div",{className:"text-6xl font-bold text-gray-300 mb-4",children:"404"}),(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"页面未找到"}),(0,t.jsx)("p",{className:"text-gray-600 mb-8",children:"抱歉，您访问的页面不存在或已被移除。"}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,t.jsxs)(l(),{href:"/",className:"btn-primary flex items-center justify-center space-x-2",children:[(0,t.jsx)(i.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:"返回首页"})]}),(0,t.jsx)(l(),{href:"/forum",className:"btn-secondary flex items-center justify-center space-x-2",children:(0,t.jsx)("span",{children:"进入论坛"})})]})]})})}},4431:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>c,metadata:()=>d});var t=r(7413),a=r(2202),l=r.n(a),i=r(4988),n=r.n(i);r(1135);let d={title:"八字论坛 - 新闻自由 舆论监督",description:"面向大学生的公共议题内容型社交平台，以新闻自由、舆论监督为核心价值",keywords:"新闻自由,舆论监督,大学生,公共议题,社交平台"};function c({children:e}){return(0,t.jsx)("html",{lang:"zh-CN",children:(0,t.jsx)("body",{className:`${l().variable} ${n().variable} antialiased`,children:e})})}},6821:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,4536,23))},7481:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>i.a,__next_app__:()=>o,pages:()=>x,routeModule:()=>m,tree:()=>c});var t=r(5239),a=r(8088),l=r(8170),i=r.n(l),n=r(893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);r.d(s,d);let c={children:["",{children:["freemedia",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,8971)),"/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/app/freemedia/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,9645))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,4413)),"/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,9645))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,x=["/home/<USER>/Desktop/vscode/新闻自由/baziforum/src/app/freemedia/page.tsx"],o={require:r,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/freemedia/page",pathname:"/freemedia",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},8129:()=>{},8971:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>m});var t=r(7413),a=r(4536),l=r.n(a),i=r(1465),n=r(6373);let d=(0,n.A)("newspaper",[["path",{d:"M15 18h-5",key:"95g1m2"}],["path",{d:"M18 14h-8",key:"sponae"}],["path",{d:"M4 22h16a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H8a2 2 0 0 0-2 2v16a2 2 0 0 1-4 0v-9a2 2 0 0 1 2-2h2",key:"39pd36"}],["rect",{width:"8",height:"4",x:"10",y:"6",rx:"1",key:"aywv1n"}]]);var c=r(1382);let x=(0,n.A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]),o=(0,n.A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]);function m(){return(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,t.jsx)("header",{className:"bg-white border-b border-gray-200",children:(0,t.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsx)("div",{className:"flex items-center h-16",children:(0,t.jsxs)(l(),{href:"/",className:"flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors",children:[(0,t.jsx)(i.A,{className:"w-5 h-5"}),(0,t.jsx)("span",{children:"返回首页"})]})})})}),(0,t.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-8",children:[(0,t.jsxs)("div",{className:"text-center mb-12",children:[(0,t.jsx)("div",{className:"flex justify-center mb-6",children:(0,t.jsx)("div",{className:"p-4 bg-blue-100 rounded-full",children:(0,t.jsx)(d,{className:"w-12 h-12 text-blue-600"})})}),(0,t.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"新闻自由"}),(0,t.jsx)("p",{className:"text-xl text-gray-600",children:"保障公众知情权，使真实不被扭曲"})]}),(0,t.jsxs)("div",{className:"prose max-w-none",children:[(0,t.jsxs)("div",{className:"bg-blue-50 rounded-lg p-6 mb-8",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-blue-900 mb-4",children:"什么是新闻自由？"}),(0,t.jsx)("p",{className:"text-blue-800 leading-relaxed",children:"新闻自由是指媒体和记者有权自由地收集、报道和传播信息，不受政府、企业或其他权力机构的不当干预。 它是民主社会的基石，确保公众能够获得准确、及时、多元的信息。"})]}),(0,t.jsxs)("div",{className:"grid md:grid-cols-3 gap-6 mb-12",children:[(0,t.jsxs)("div",{className:"text-center p-6 bg-gray-50 rounded-lg",children:[(0,t.jsx)(c.A,{className:"w-8 h-8 mx-auto mb-4 text-green-600"}),(0,t.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"知情权"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"公众有权了解影响他们生活的事件和决策"})]}),(0,t.jsxs)("div",{className:"text-center p-6 bg-gray-50 rounded-lg",children:[(0,t.jsx)(x,{className:"w-8 h-8 mx-auto mb-4 text-blue-600"}),(0,t.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"监督权力"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"媒体作为第四权力，监督政府和企业行为"})]}),(0,t.jsxs)("div",{className:"text-center p-6 bg-gray-50 rounded-lg",children:[(0,t.jsx)(o,{className:"w-8 h-8 mx-auto mb-4 text-purple-600"}),(0,t.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"多元声音"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"保障不同观点和声音都能被听见"})]})]}),(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"为什么新闻自由如此重要？"}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"border-l-4 border-blue-500 pl-6",children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"防止信息垄断"}),(0,t.jsx)("p",{className:"text-gray-700",children:"当信息被少数人控制时，真相可能被扭曲或隐瞒。新闻自由确保信息来源的多样性， 让公众能够从不同角度了解事件真相。"})]}),(0,t.jsxs)("div",{className:"border-l-4 border-green-500 pl-6",children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"促进民主参与"}),(0,t.jsx)("p",{className:"text-gray-700",children:"只有在充分了解情况的基础上，公民才能做出明智的政治选择。 新闻自由为民主决策提供了必要的信息基础。"})]}),(0,t.jsxs)("div",{className:"border-l-4 border-purple-500 pl-6",children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"保护弱势群体"}),(0,t.jsx)("p",{className:"text-gray-700",children:"媒体报道能够揭露不公正现象，为弱势群体发声， 推动社会关注和解决问题。"})]}),(0,t.jsxs)("div",{className:"border-l-4 border-orange-500 pl-6",children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"推动社会进步"}),(0,t.jsx)("p",{className:"text-gray-700",children:"通过揭露问题、引发讨论，新闻报道能够推动政策改革和社会进步， 促进更加公正和透明的社会环境。"})]})]})]}),(0,t.jsxs)("div",{className:"bg-yellow-50 rounded-lg p-6 mb-8",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-yellow-900 mb-4",children:"现实中的挑战"}),(0,t.jsxs)("ul",{className:"space-y-3 text-yellow-800",children:[(0,t.jsxs)("li",{className:"flex items-start",children:[(0,t.jsx)("span",{className:"text-yellow-600 mr-2",children:"•"}),"政府审查和媒体管制"]}),(0,t.jsxs)("li",{className:"flex items-start",children:[(0,t.jsx)("span",{className:"text-yellow-600 mr-2",children:"•"}),"商业利益对新闻独立性的影响"]}),(0,t.jsxs)("li",{className:"flex items-start",children:[(0,t.jsx)("span",{className:"text-yellow-600 mr-2",children:"•"}),"虚假信息和谣言的传播"]}),(0,t.jsxs)("li",{className:"flex items-start",children:[(0,t.jsx)("span",{className:"text-yellow-600 mr-2",children:"•"}),"记者人身安全受到威胁"]}),(0,t.jsxs)("li",{className:"flex items-start",children:[(0,t.jsx)("span",{className:"text-yellow-600 mr-2",children:"•"}),"技术平台的算法控制"]})]})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"我们能做什么？"}),(0,t.jsx)("p",{className:"text-gray-700 mb-6",children:"作为公民，我们每个人都可以为维护新闻自由贡献力量："}),(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-4 text-left mb-8",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("div",{children:"✓ 支持独立媒体和调查报道"}),(0,t.jsx)("div",{children:"✓ 培养媒体素养，学会辨别信息"}),(0,t.jsx)("div",{children:"✓ 关注和分享有价值的新闻内容"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("div",{children:"✓ 参与公共讨论，表达理性观点"}),(0,t.jsx)("div",{children:"✓ 监督和举报虚假信息"}),(0,t.jsx)("div",{children:"✓ 保护记者和媒体工作者的权益"})]})]}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,t.jsx)(l(),{href:"/publicwatch",className:"btn-primary",children:"了解舆论监督"}),(0,t.jsx)(l(),{href:"/join",className:"btn-secondary",children:"加入我们"})]})]})]})]})})]})}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")},9636:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},9645:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>a});var t=r(1658);let a=async e=>[{type:"image/svg+xml",sizes:"any",url:(0,t.fillMetadataSegment)(".",await e.params,"icon.svg")+"?e976668d334838b1"}]},9780:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[447,30],()=>r(7481));module.exports=t})();