exports.id=7,exports.ids=[7],exports.modules={440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(1658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},1135:()=>{},1681:()=>{},2688:(e,t,r)=>{"use strict";r.d(t,{A:()=>m});var s=r(3210);let i=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),n=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),a=e=>{let t=n(e);return t.charAt(0).toUpperCase()+t.slice(1)},o=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim(),c=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,s.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:i,className:n="",children:a,iconNode:d,...m},u)=>(0,s.createElement)("svg",{ref:u,...l,width:t,height:t,stroke:e,strokeWidth:i?24*Number(r)/Number(t):r,className:o("lucide",n),...!a&&!c(m)&&{"aria-hidden":"true"},...m},[...d.map(([e,t])=>(0,s.createElement)(e,t)),...Array.isArray(a)?a:[a]])),m=(e,t)=>{let r=(0,s.forwardRef)(({className:r,...n},c)=>(0,s.createElement)(d,{ref:c,iconNode:t,className:o(`lucide-${i(a(e))}`,`lucide-${e}`,r),...n}));return r.displayName=a(e),r}},3269:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,5814,23))},3861:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(2688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3872:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(2688).A)("message-circle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},4413:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var s=r(7413),i=r(4536),n=r.n(i),a=r(8898);function o(){return(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"max-w-md mx-auto text-center",children:[(0,s.jsx)("div",{className:"text-6xl font-bold text-gray-300 mb-4",children:"404"}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"页面未找到"}),(0,s.jsx)("p",{className:"text-gray-600 mb-8",children:"抱歉，您访问的页面不存在或已被移除。"}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,s.jsxs)(n(),{href:"/",className:"btn-primary flex items-center justify-center space-x-2",children:[(0,s.jsx)(a.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"返回首页"})]}),(0,s.jsx)(n(),{href:"/forum",className:"btn-secondary flex items-center justify-center space-x-2",children:(0,s.jsx)("span",{children:"进入论坛"})})]})]})})}},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l,metadata:()=>c});var s=r(7413),i=r(2202),n=r.n(i),a=r(4988),o=r.n(a);r(1135);let c={title:"八字论坛 - 新闻自由 舆论监督",description:"面向大学生的公共议题内容型社交平台，以新闻自由、舆论监督为核心价值",keywords:"新闻自由,舆论监督,大学生,公共议题,社交平台"};function l({children:e}){return(0,s.jsx)("html",{lang:"zh-CN",children:(0,s.jsx)("body",{className:`${n().variable} ${o().variable} antialiased`,children:e})})}},4780:(e,t,r)=>{"use strict";function s(e){let t=new Date(e),r=Math.floor((new Date().getTime()-t.getTime())/1e3);if(r<60)return"刚刚";if(r<3600){let e=Math.floor(r/60);return`${e}分钟前`}if(r<86400){let e=Math.floor(r/3600);return`${e}小时前`}{if(!(r<2592e3))return t.toLocaleDateString("zh-CN");let e=Math.floor(r/86400);return`${e}天前`}}function i(e){return e<1e3?e.toString():e<1e4?`${(e/1e3).toFixed(1)}k`:`${(e/1e4).toFixed(1)}w`}r.d(t,{Yq:()=>s,ZV:()=>i})},6189:(e,t,r)=>{"use strict";var s=r(5773);r.o(s,"notFound")&&r.d(t,{notFound:function(){return s.notFound}}),r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}})},6821:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,4536,23))},6973:(e,t,r)=>{"use strict";r.d(t,{G:()=>s});let s=[{id:"1",title:"高校学生举报教授学术不端事件持续发酵",content:"近日，某知名高校学生实名举报导师学术不端的事件在网络上引起广泛关注。该学生通过社交媒体详细披露了导师在学术研究中存在的数据造假、剽窃他人成果等问题...",excerpt:"学生实名举报导师学术不端，引发社会对高校学术诚信的广泛讨论",author:"新闻观察员",category:"新",createdAt:"2024-01-15T10:30:00Z",updatedAt:"2024-01-15T10:30:00Z",likes:156,comments:23,views:1205,tags:["学术诚信","高校","举报"],isHot:!0},{id:"2",title:"某地环保数据造假案件调查进展",content:"环保部门数据造假案件调查取得重要进展，多名相关责任人被立案调查。此案暴露出环境监测数据造假的严重问题...",excerpt:"环保数据造假案调查进展，多人被立案调查",author:"环保关注者",category:"新",createdAt:"2024-01-14T15:20:00Z",updatedAt:"2024-01-14T15:20:00Z",likes:89,comments:15,views:756,tags:["环保","数据造假","调查"]},{id:"3",title:"回顾：2023年度十大新闻自由事件",content:"2023年是新闻自由面临重大挑战的一年。从记者采访权受限到媒体报道空间收窄，我们见证了多起影响深远的事件...",excerpt:"盘点2023年影响新闻自由的重大事件及其深远影响",author:"媒体研究者",category:"闻",createdAt:"2024-01-10T09:00:00Z",updatedAt:"2024-01-10T09:00:00Z",likes:234,comments:45,views:2103,tags:["新闻自由","年度盘点","媒体"],isSticky:!0},{id:"4",title:"作为大学生，我们为什么需要关注新闻自由",content:"很多同学觉得新闻自由离我们很远，但实际上它与我们的日常生活息息相关。当我们无法获得真实信息时，我们的判断就会出现偏差...",excerpt:"一名大学生对新闻自由重要性的思考和感悟",author:"思考中的学生",category:"自",createdAt:"2024-01-13T20:15:00Z",updatedAt:"2024-01-13T20:15:00Z",likes:67,comments:12,views:445,tags:["大学生","思考","新闻自由"]},{id:"5",title:"如何识别和应对网络谣言：一份实用指南",content:"在信息爆炸的时代，谣言传播速度极快。本文将教你如何通过多种方法验证信息真实性，包括查证消息源、交叉验证等技巧...",excerpt:"教你如何在信息时代识别和应对网络谣言的实用方法",author:"事实核查员",category:"由",createdAt:"2024-01-12T14:30:00Z",updatedAt:"2024-01-12T14:30:00Z",likes:145,comments:28,views:892,tags:["谣言识别","事实核查","媒体素养"]},{id:"6",title:"社交媒体算法如何影响我们的信息获取",content:"社交媒体平台的推荐算法正在深刻影响着我们接收信息的方式。算法会根据我们的行为偏好推送内容，这可能导致信息茧房效应...",excerpt:"分析社交媒体算法对信息传播和公众认知的影响",author:"算法观察者",category:"舆",createdAt:"2024-01-11T16:45:00Z",updatedAt:"2024-01-11T16:45:00Z",likes:198,comments:34,views:1156,tags:["算法","信息茧房","社交媒体"]},{id:"7",title:"辩论：网络实名制是否有利于净化网络环境？",content:"正方观点：网络实名制能够有效减少网络暴力和虚假信息传播，提高网络言论质量。反方观点：实名制可能限制言论自由，让人们不敢表达真实想法...",excerpt:"关于网络实名制利弊的深度辩论，欢迎参与讨论",author:"辩论主持人",category:"论",createdAt:"2024-01-09T11:20:00Z",updatedAt:"2024-01-09T11:20:00Z",likes:89,comments:67,views:1334,tags:["网络实名制","言论自由","辩论"]},{id:"8",title:"用户举报：某自媒体传播不实信息案例分析",content:"近期收到用户举报，某自媒体账号发布了关于疫苗安全性的不实信息。经过调查核实，该信息确实存在误导性内容...",excerpt:"用户举报自媒体传播不实信息的案例分析和处理结果",author:"社区管理员",category:"监",createdAt:"2024-01-08T13:10:00Z",updatedAt:"2024-01-08T13:10:00Z",likes:76,comments:19,views:623,tags:["举报","不实信息","自媒体"]},{id:"9",title:"八字论坛开发进展报告 - 2024年1月",content:"本月开发进展：完成了基础框架搭建，实现了八个板块的基本功能，优化了移动端体验。下月计划：增加用户系统，完善评论功能...",excerpt:"八字论坛项目最新开发进展和下阶段计划",author:"项目负责人",category:"督",createdAt:"2024-01-07T10:00:00Z",updatedAt:"2024-01-07T10:00:00Z",likes:45,comments:8,views:234,tags:["开发进展","项目更新","社区建设"],isSticky:!0}]},7760:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(2688).A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},8129:()=>{},8559:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(2688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},9636:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},9780:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))}};