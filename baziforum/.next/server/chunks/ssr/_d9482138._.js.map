{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/vscode/%E6%96%B0%E9%97%BB%E8%87%AA%E7%94%B1/baziforum/src/data/sections.ts"], "sourcesContent": ["import { BaziSection } from '@/types';\n\nexport const baziSections: BaziSection[] = [\n  {\n    key: '新',\n    slug: 'latest',\n    name: '最新事件',\n    description: '浏览当前社会热点内容，作为热榜入口',\n    color: 'bg-red-500',\n    icon: 'Flame'\n  },\n  {\n    key: '闻',\n    slug: 'archives',\n    name: '重大事件档案',\n    description: '汇总关键事件、转折记录，构成\"公共记忆\"',\n    color: 'bg-blue-500',\n    icon: 'Archive'\n  },\n  {\n    key: '自',\n    slug: 'opinions',\n    name: '个人观点发表',\n    description: '自由表达区，含日志、原创内容等',\n    color: 'bg-green-500',\n    icon: 'PenTool'\n  },\n  {\n    key: '由',\n    slug: 'factcheck',\n    name: '假新闻溯源',\n    description: '提供谣言分析、信息溯源内容，引导辟谣意识',\n    color: 'bg-yellow-500',\n    icon: 'SearchCheck'\n  },\n  {\n    key: '舆',\n    slug: 'mediawatch',\n    name: '媒体观察',\n    description: '分析媒体偏向、舆论塑造机制，促思辨',\n    color: 'bg-purple-500',\n    icon: 'Eye'\n  },\n  {\n    key: '论',\n    slug: 'debates',\n    name: '话题辩论场',\n    description: '开设辩题，设立正反立场，引导参与讨论',\n    color: 'bg-indigo-500',\n    icon: 'MessageSquare'\n  },\n  {\n    key: '监',\n    slug: 'reports',\n    name: '举报与公审',\n    description: '用户举报展示，管理员与用户共同评价',\n    color: 'bg-orange-500',\n    icon: 'Scale'\n  },\n  {\n    key: '督',\n    slug: 'community',\n    name: '社区进展公告',\n    description: '开发日志、任务列表、人员招募、制度建设',\n    color: 'bg-gray-500',\n    icon: 'ClipboardList'\n  }\n];\n"], "names": [], "mappings": ";;;AAEO,MAAM,eAA8B;IACzC;QACE,KAAK;QACL,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,MAAM;IACR;IACA;QACE,KAAK;QACL,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,MAAM;IACR;IACA;QACE,KAAK;QACL,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,MAAM;IACR;IACA;QACE,KAAK;QACL,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,MAAM;IACR;IACA;QACE,KAAK;QACL,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,MAAM;IACR;IACA;QACE,KAAK;QACL,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,MAAM;IACR;IACA;QACE,KAAK;QACL,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,MAAM;IACR;IACA;QACE,KAAK;QACL,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,MAAM;IACR;CACD", "debugId": null}}, {"offset": {"line": 105, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/vscode/%E6%96%B0%E9%97%BB%E8%87%AA%E7%94%B1/baziforum/src/data/posts.ts"], "sourcesContent": ["import { Post } from '@/types';\n\nexport const samplePosts: Post[] = [\n  // 新 - 最新事件\n  {\n    id: '1',\n    title: '高校学生举报教授学术不端事件持续发酵',\n    content: '近日，某知名高校学生实名举报导师学术不端的事件在网络上引起广泛关注。该学生通过社交媒体详细披露了导师在学术研究中存在的数据造假、剽窃他人成果等问题...',\n    excerpt: '学生实名举报导师学术不端，引发社会对高校学术诚信的广泛讨论',\n    author: '新闻观察员',\n    category: '新',\n    createdAt: '2024-01-15T10:30:00Z',\n    updatedAt: '2024-01-15T10:30:00Z',\n    likes: 156,\n    comments: 23,\n    views: 1205,\n    tags: ['学术诚信', '高校', '举报'],\n    isHot: true\n  },\n  {\n    id: '2',\n    title: '某地环保数据造假案件调查进展',\n    content: '环保部门数据造假案件调查取得重要进展，多名相关责任人被立案调查。此案暴露出环境监测数据造假的严重问题...',\n    excerpt: '环保数据造假案调查进展，多人被立案调查',\n    author: '环保关注者',\n    category: '新',\n    createdAt: '2024-01-14T15:20:00Z',\n    updatedAt: '2024-01-14T15:20:00Z',\n    likes: 89,\n    comments: 15,\n    views: 756,\n    tags: ['环保', '数据造假', '调查']\n  },\n\n  // 闻 - 重大事件档案\n  {\n    id: '3',\n    title: '回顾：2023年度十大新闻自由事件',\n    content: '2023年是新闻自由面临重大挑战的一年。从记者采访权受限到媒体报道空间收窄，我们见证了多起影响深远的事件...',\n    excerpt: '盘点2023年影响新闻自由的重大事件及其深远影响',\n    author: '媒体研究者',\n    category: '闻',\n    createdAt: '2024-01-10T09:00:00Z',\n    updatedAt: '2024-01-10T09:00:00Z',\n    likes: 234,\n    comments: 45,\n    views: 2103,\n    tags: ['新闻自由', '年度盘点', '媒体'],\n    isSticky: true\n  },\n\n  // 自 - 个人观点发表\n  {\n    id: '4',\n    title: '作为大学生，我们为什么需要关注新闻自由',\n    content: '很多同学觉得新闻自由离我们很远，但实际上它与我们的日常生活息息相关。当我们无法获得真实信息时，我们的判断就会出现偏差...',\n    excerpt: '一名大学生对新闻自由重要性的思考和感悟',\n    author: '思考中的学生',\n    category: '自',\n    createdAt: '2024-01-13T20:15:00Z',\n    updatedAt: '2024-01-13T20:15:00Z',\n    likes: 67,\n    comments: 12,\n    views: 445,\n    tags: ['大学生', '思考', '新闻自由']\n  },\n\n  // 由 - 假新闻溯源\n  {\n    id: '5',\n    title: '如何识别和应对网络谣言：一份实用指南',\n    content: '在信息爆炸的时代，谣言传播速度极快。本文将教你如何通过多种方法验证信息真实性，包括查证消息源、交叉验证等技巧...',\n    excerpt: '教你如何在信息时代识别和应对网络谣言的实用方法',\n    author: '事实核查员',\n    category: '由',\n    createdAt: '2024-01-12T14:30:00Z',\n    updatedAt: '2024-01-12T14:30:00Z',\n    likes: 145,\n    comments: 28,\n    views: 892,\n    tags: ['谣言识别', '事实核查', '媒体素养']\n  },\n\n  // 舆 - 媒体观察\n  {\n    id: '6',\n    title: '社交媒体算法如何影响我们的信息获取',\n    content: '社交媒体平台的推荐算法正在深刻影响着我们接收信息的方式。算法会根据我们的行为偏好推送内容，这可能导致信息茧房效应...',\n    excerpt: '分析社交媒体算法对信息传播和公众认知的影响',\n    author: '算法观察者',\n    category: '舆',\n    createdAt: '2024-01-11T16:45:00Z',\n    updatedAt: '2024-01-11T16:45:00Z',\n    likes: 198,\n    comments: 34,\n    views: 1156,\n    tags: ['算法', '信息茧房', '社交媒体']\n  },\n\n  // 论 - 话题辩论场\n  {\n    id: '7',\n    title: '辩论：网络实名制是否有利于净化网络环境？',\n    content: '正方观点：网络实名制能够有效减少网络暴力和虚假信息传播，提高网络言论质量。反方观点：实名制可能限制言论自由，让人们不敢表达真实想法...',\n    excerpt: '关于网络实名制利弊的深度辩论，欢迎参与讨论',\n    author: '辩论主持人',\n    category: '论',\n    createdAt: '2024-01-09T11:20:00Z',\n    updatedAt: '2024-01-09T11:20:00Z',\n    likes: 89,\n    comments: 67,\n    views: 1334,\n    tags: ['网络实名制', '言论自由', '辩论']\n  },\n\n  // 监 - 举报与公审\n  {\n    id: '8',\n    title: '用户举报：某自媒体传播不实信息案例分析',\n    content: '近期收到用户举报，某自媒体账号发布了关于疫苗安全性的不实信息。经过调查核实，该信息确实存在误导性内容...',\n    excerpt: '用户举报自媒体传播不实信息的案例分析和处理结果',\n    author: '社区管理员',\n    category: '监',\n    createdAt: '2024-01-08T13:10:00Z',\n    updatedAt: '2024-01-08T13:10:00Z',\n    likes: 76,\n    comments: 19,\n    views: 623,\n    tags: ['举报', '不实信息', '自媒体']\n  },\n\n  // 督 - 社区进展公告\n  {\n    id: '9',\n    title: '八字论坛开发进展报告 - 2024年1月',\n    content: '本月开发进展：完成了基础框架搭建，实现了八个板块的基本功能，优化了移动端体验。下月计划：增加用户系统，完善评论功能...',\n    excerpt: '八字论坛项目最新开发进展和下阶段计划',\n    author: '项目负责人',\n    category: '督',\n    createdAt: '2024-01-07T10:00:00Z',\n    updatedAt: '2024-01-07T10:00:00Z',\n    likes: 45,\n    comments: 8,\n    views: 234,\n    tags: ['开发进展', '项目更新', '社区建设'],\n    isSticky: true\n  },\n\n  // 更多新闻事件\n  {\n    id: '10',\n    title: '某地政府信息公开申请遭拒，律师提起行政诉讼',\n    content: '公益律师向某地政府申请公开环境监测数据，遭到拒绝后提起行政诉讼。此案引发对政府信息公开制度执行情况的关注...',\n    excerpt: '政府信息公开申请被拒引发行政诉讼，考验信息透明度',\n    author: '法律观察员',\n    category: '新',\n    createdAt: '2024-01-16T14:20:00Z',\n    updatedAt: '2024-01-16T14:20:00Z',\n    likes: 78,\n    comments: 12,\n    views: 567,\n    tags: ['信息公开', '行政诉讼', '政府透明度']\n  },\n\n  {\n    id: '11',\n    title: '网络主播传播虚假疫情信息被处罚案例分析',\n    content: '某知名网络主播在直播中传播未经证实的疫情信息，被相关部门处罚。此案反映出网络信息传播的责任问题...',\n    excerpt: '网络主播传播虚假疫情信息被罚，网络传播责任受关注',\n    author: '网络观察者',\n    category: '新',\n    createdAt: '2024-01-15T16:30:00Z',\n    updatedAt: '2024-01-15T16:30:00Z',\n    likes: 92,\n    comments: 18,\n    views: 743,\n    tags: ['网络主播', '虚假信息', '疫情']\n  },\n\n  // 更多档案内容\n  {\n    id: '12',\n    title: '回顾：互联网时代的新闻自由里程碑事件',\n    content: '从博客兴起到社交媒体普及，从公民记者到自媒体时代，互联网深刻改变了新闻传播格局。本文梳理了互联网时代新闻自由发展的关键节点...',\n    excerpt: '梳理互联网时代新闻自由发展的重要历史节点',\n    author: '媒体史研究者',\n    category: '闻',\n    createdAt: '2024-01-14T11:00:00Z',\n    updatedAt: '2024-01-14T11:00:00Z',\n    likes: 156,\n    comments: 24,\n    views: 1234,\n    tags: ['互联网', '新闻史', '媒体发展'],\n    isHot: true\n  },\n\n  {\n    id: '13',\n    title: '档案：历次重大公共卫生事件中的信息公开',\n    content: '从SARS到新冠疫情，重大公共卫生事件中的信息公开一直是社会关注焦点。本文整理了历次疫情中信息公开的经验教训...',\n    excerpt: '回顾重大疫情中信息公开的历史经验与教训',\n    author: '公共卫生观察员',\n    category: '闻',\n    createdAt: '2024-01-13T09:15:00Z',\n    updatedAt: '2024-01-13T09:15:00Z',\n    likes: 134,\n    comments: 31,\n    views: 987,\n    tags: ['公共卫生', '信息公开', '疫情']\n  },\n\n  // 更多个人观点\n  {\n    id: '14',\n    title: '从一名新闻系学生的角度看媒体素养教育',\n    content: '作为新闻系的学生，我深感媒体素养教育的重要性。在信息爆炸的时代，如何培养批判性思维，如何识别可靠信息源...',\n    excerpt: '新闻系学生分享对媒体素养教育重要性的思考',\n    author: '新闻系学生',\n    category: '自',\n    createdAt: '2024-01-16T19:30:00Z',\n    updatedAt: '2024-01-16T19:30:00Z',\n    likes: 89,\n    comments: 15,\n    views: 456,\n    tags: ['媒体素养', '新闻教育', '批判思维']\n  },\n\n  {\n    id: '15',\n    title: '我为什么选择成为一名公民记者',\n    content: '在传统媒体之外，公民记者正在发挥越来越重要的作用。我分享自己成为公民记者的经历，以及在这个过程中的思考...',\n    excerpt: '一位公民记者分享自己的从业经历和思考',\n    author: '公民记者小王',\n    category: '自',\n    createdAt: '2024-01-15T21:45:00Z',\n    updatedAt: '2024-01-15T21:45:00Z',\n    likes: 67,\n    comments: 9,\n    views: 378,\n    tags: ['公民记者', '媒体参与', '新闻实践']\n  },\n\n  // 更多假新闻溯源\n  {\n    id: '16',\n    title: '深度分析：某健康谣言的传播路径与辟谣过程',\n    content: '一条关于某食品致癌的谣言在网络上快速传播，我们追踪了这条谣言的传播路径，分析了辟谣的过程和效果...',\n    excerpt: '追踪健康谣言传播路径，分析辟谣机制的有效性',\n    author: '谣言追踪者',\n    category: '由',\n    createdAt: '2024-01-16T13:20:00Z',\n    updatedAt: '2024-01-16T13:20:00Z',\n    likes: 112,\n    comments: 22,\n    views: 689,\n    tags: ['健康谣言', '传播路径', '辟谣机制']\n  },\n\n  {\n    id: '17',\n    title: '如何利用开源情报验证网络传言',\n    content: '开源情报（OSINT）技术为普通人验证网络信息提供了新的工具。本文介绍几种常用的开源情报验证方法...',\n    excerpt: '介绍开源情报技术在信息验证中的应用方法',\n    author: 'OSINT研究员',\n    category: '由',\n    createdAt: '2024-01-14T17:10:00Z',\n    updatedAt: '2024-01-14T17:10:00Z',\n    likes: 145,\n    comments: 18,\n    views: 823,\n    tags: ['开源情报', 'OSINT', '信息验证']\n  },\n\n  // 更多媒体观察\n  {\n    id: '18',\n    title: '短视频平台的信息传播特点与监管挑战',\n    content: '短视频平台已成为重要的信息传播渠道，但其传播特点也带来了新的监管挑战。本文分析短视频信息传播的特点...',\n    excerpt: '分析短视频平台信息传播特点及其带来的监管挑战',\n    author: '新媒体研究者',\n    category: '舆',\n    createdAt: '2024-01-16T10:30:00Z',\n    updatedAt: '2024-01-16T10:30:00Z',\n    likes: 98,\n    comments: 16,\n    views: 567,\n    tags: ['短视频', '信息传播', '平台监管']\n  },\n\n  {\n    id: '19',\n    title: '人工智能时代的新闻生产与伦理思考',\n    content: 'AI技术在新闻生产中的应用越来越广泛，从自动写稿到深度伪造，技术进步带来便利的同时也引发伦理担忧...',\n    excerpt: '探讨AI技术在新闻领域应用的机遇与伦理挑战',\n    author: 'AI伦理研究者',\n    category: '舆',\n    createdAt: '2024-01-15T14:45:00Z',\n    updatedAt: '2024-01-15T14:45:00Z',\n    likes: 167,\n    comments: 28,\n    views: 934,\n    tags: ['人工智能', '新闻伦理', '技术影响'],\n    isHot: true\n  },\n\n  // 更多辩论话题\n  {\n    id: '20',\n    title: '辩论：社交媒体平台是否应该对用户言论承担更多责任？',\n    content: '正方：平台作为信息传播的重要渠道，应该承担更多审核责任，防止有害信息传播。反方：过度审核可能导致言论自由受限...',\n    excerpt: '关于社交媒体平台言论责任的深度辩论',\n    author: '辩论组织者',\n    category: '论',\n    createdAt: '2024-01-16T15:20:00Z',\n    updatedAt: '2024-01-16T15:20:00Z',\n    likes: 134,\n    comments: 45,\n    views: 876,\n    tags: ['社交媒体', '言论责任', '平台治理']\n  },\n\n  {\n    id: '21',\n    title: '辩论：新闻付费墙是促进还是阻碍了信息传播？',\n    content: '正方：付费墙保障了新闻机构的收入，有利于高质量新闻生产。反方：付费墙加剧了信息不平等，阻碍了信息的自由流动...',\n    excerpt: '关于新闻付费模式利弊的辩论讨论',\n    author: '媒体经济观察员',\n    category: '论',\n    createdAt: '2024-01-15T11:30:00Z',\n    updatedAt: '2024-01-15T11:30:00Z',\n    likes: 87,\n    comments: 33,\n    views: 654,\n    tags: ['付费新闻', '媒体经济', '信息平等']\n  },\n\n  // 更多举报监督\n  {\n    id: '22',\n    title: '举报：某地方媒体涉嫌收费删帖行为调查',\n    content: '接到举报称某地方媒体存在收费删帖行为，经调查核实，确实存在违规操作。现将调查结果公布，并呼吁加强媒体监管...',\n    excerpt: '地方媒体收费删帖行为调查结果公布',\n    author: '监督委员会',\n    category: '监',\n    createdAt: '2024-01-16T12:00:00Z',\n    updatedAt: '2024-01-16T12:00:00Z',\n    likes: 156,\n    comments: 27,\n    views: 789,\n    tags: ['收费删帖', '媒体监管', '新闻伦理']\n  },\n\n  {\n    id: '23',\n    title: '公众举报：网络大V传播不实金融信息案例',\n    content: '某网络大V在社交平台发布不实金融信息，误导投资者决策。经举报和调查，相关部门已对其进行处理...',\n    excerpt: '网络大V传播不实金融信息被举报处理',\n    author: '金融监督员',\n    category: '监',\n    createdAt: '2024-01-14T16:15:00Z',\n    updatedAt: '2024-01-14T16:15:00Z',\n    likes: 98,\n    comments: 19,\n    views: 543,\n    tags: ['网络大V', '金融信息', '投资误导']\n  },\n\n  // 更多社区公告\n  {\n    id: '24',\n    title: '八字论坛用户行为规范更新公告',\n    content: '为了维护良好的社区环境，我们更新了用户行为规范。新规范明确了发帖标准、评论要求、举报流程等内容...',\n    excerpt: '社区用户行为规范更新，明确发帖和互动标准',\n    author: '社区管理员',\n    category: '督',\n    createdAt: '2024-01-16T09:00:00Z',\n    updatedAt: '2024-01-16T09:00:00Z',\n    likes: 67,\n    comments: 12,\n    views: 345,\n    tags: ['用户规范', '社区管理', '行为准则'],\n    isSticky: true\n  },\n\n  {\n    id: '25',\n    title: '招募：寻找志同道合的内容审核志愿者',\n    content: '随着社区规模扩大，我们需要更多志愿者参与内容审核工作。如果你关注新闻自由和舆论监督，欢迎加入我们...',\n    excerpt: '招募内容审核志愿者，共同维护社区环境',\n    author: '志愿者协调员',\n    category: '督',\n    createdAt: '2024-01-15T10:30:00Z',\n    updatedAt: '2024-01-15T10:30:00Z',\n    likes: 89,\n    comments: 15,\n    views: 432,\n    tags: ['志愿者招募', '内容审核', '社区建设']\n  }\n];\n"], "names": [], "mappings": ";;;AAEO,MAAM,cAAsB;IACjC,WAAW;IACX;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,UAAU;QACV,WAAW;QACX,WAAW;QACX,OAAO;QACP,UAAU;QACV,OAAO;QACP,MAAM;YAAC;YAAQ;YAAM;SAAK;QAC1B,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,UAAU;QACV,WAAW;QACX,WAAW;QACX,OAAO;QACP,UAAU;QACV,OAAO;QACP,MAAM;YAAC;YAAM;YAAQ;SAAK;IAC5B;IAEA,aAAa;IACb;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,UAAU;QACV,WAAW;QACX,WAAW;QACX,OAAO;QACP,UAAU;QACV,OAAO;QACP,MAAM;YAAC;YAAQ;YAAQ;SAAK;QAC5B,UAAU;IACZ;IAEA,aAAa;IACb;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,UAAU;QACV,WAAW;QACX,WAAW;QACX,OAAO;QACP,UAAU;QACV,OAAO;QACP,MAAM;YAAC;YAAO;YAAM;SAAO;IAC7B;IAEA,YAAY;IACZ;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,UAAU;QACV,WAAW;QACX,WAAW;QACX,OAAO;QACP,UAAU;QACV,OAAO;QACP,MAAM;YAAC;YAAQ;YAAQ;SAAO;IAChC;IAEA,WAAW;IACX;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,UAAU;QACV,WAAW;QACX,WAAW;QACX,OAAO;QACP,UAAU;QACV,OAAO;QACP,MAAM;YAAC;YAAM;YAAQ;SAAO;IAC9B;IAEA,YAAY;IACZ;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,UAAU;QACV,WAAW;QACX,WAAW;QACX,OAAO;QACP,UAAU;QACV,OAAO;QACP,MAAM;YAAC;YAAS;YAAQ;SAAK;IAC/B;IAEA,YAAY;IACZ;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,UAAU;QACV,WAAW;QACX,WAAW;QACX,OAAO;QACP,UAAU;QACV,OAAO;QACP,MAAM;YAAC;YAAM;YAAQ;SAAM;IAC7B;IAEA,aAAa;IACb;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,UAAU;QACV,WAAW;QACX,WAAW;QACX,OAAO;QACP,UAAU;QACV,OAAO;QACP,MAAM;YAAC;YAAQ;YAAQ;SAAO;QAC9B,UAAU;IACZ;IAEA,SAAS;IACT;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,UAAU;QACV,WAAW;QACX,WAAW;QACX,OAAO;QACP,UAAU;QACV,OAAO;QACP,MAAM;YAAC;YAAQ;YAAQ;SAAQ;IACjC;IAEA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,UAAU;QACV,WAAW;QACX,WAAW;QACX,OAAO;QACP,UAAU;QACV,OAAO;QACP,MAAM;YAAC;YAAQ;YAAQ;SAAK;IAC9B;IAEA,SAAS;IACT;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,UAAU;QACV,WAAW;QACX,WAAW;QACX,OAAO;QACP,UAAU;QACV,OAAO;QACP,MAAM;YAAC;YAAO;YAAO;SAAO;QAC5B,OAAO;IACT;IAEA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,UAAU;QACV,WAAW;QACX,WAAW;QACX,OAAO;QACP,UAAU;QACV,OAAO;QACP,MAAM;YAAC;YAAQ;YAAQ;SAAK;IAC9B;IAEA,SAAS;IACT;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,UAAU;QACV,WAAW;QACX,WAAW;QACX,OAAO;QACP,UAAU;QACV,OAAO;QACP,MAAM;YAAC;YAAQ;YAAQ;SAAO;IAChC;IAEA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,UAAU;QACV,WAAW;QACX,WAAW;QACX,OAAO;QACP,UAAU;QACV,OAAO;QACP,MAAM;YAAC;YAAQ;YAAQ;SAAO;IAChC;IAEA,UAAU;IACV;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,UAAU;QACV,WAAW;QACX,WAAW;QACX,OAAO;QACP,UAAU;QACV,OAAO;QACP,MAAM;YAAC;YAAQ;YAAQ;SAAO;IAChC;IAEA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,UAAU;QACV,WAAW;QACX,WAAW;QACX,OAAO;QACP,UAAU;QACV,OAAO;QACP,MAAM;YAAC;YAAQ;YAAS;SAAO;IACjC;IAEA,SAAS;IACT;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,UAAU;QACV,WAAW;QACX,WAAW;QACX,OAAO;QACP,UAAU;QACV,OAAO;QACP,MAAM;YAAC;YAAO;YAAQ;SAAO;IAC/B;IAEA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,UAAU;QACV,WAAW;QACX,WAAW;QACX,OAAO;QACP,UAAU;QACV,OAAO;QACP,MAAM;YAAC;YAAQ;YAAQ;SAAO;QAC9B,OAAO;IACT;IAEA,SAAS;IACT;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,UAAU;QACV,WAAW;QACX,WAAW;QACX,OAAO;QACP,UAAU;QACV,OAAO;QACP,MAAM;YAAC;YAAQ;YAAQ;SAAO;IAChC;IAEA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,UAAU;QACV,WAAW;QACX,WAAW;QACX,OAAO;QACP,UAAU;QACV,OAAO;QACP,MAAM;YAAC;YAAQ;YAAQ;SAAO;IAChC;IAEA,SAAS;IACT;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,UAAU;QACV,WAAW;QACX,WAAW;QACX,OAAO;QACP,UAAU;QACV,OAAO;QACP,MAAM;YAAC;YAAQ;YAAQ;SAAO;IAChC;IAEA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,UAAU;QACV,WAAW;QACX,WAAW;QACX,OAAO;QACP,UAAU;QACV,OAAO;QACP,MAAM;YAAC;YAAQ;YAAQ;SAAO;IAChC;IAEA,SAAS;IACT;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,UAAU;QACV,WAAW;QACX,WAAW;QACX,OAAO;QACP,UAAU;QACV,OAAO;QACP,MAAM;YAAC;YAAQ;YAAQ;SAAO;QAC9B,UAAU;IACZ;IAEA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,UAAU;QACV,WAAW;QACX,WAAW;QACX,OAAO;QACP,UAAU;QACV,OAAO;QACP,MAAM;YAAC;YAAS;YAAQ;SAAO;IACjC;CACD", "debugId": null}}, {"offset": {"line": 588, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/vscode/%E6%96%B0%E9%97%BB%E8%87%AA%E7%94%B1/baziforum/src/components/DynamicIcon.tsx"], "sourcesContent": ["import { \n  Flame, \n  Archive, \n  PenTool, \n  SearchCheck, \n  Eye, \n  MessageSquare, \n  Scale, \n  ClipboardList \n} from 'lucide-react';\n\nconst iconMap = {\n  Flame,\n  Archive,\n  PenTool,\n  SearchCheck,\n  Eye,\n  MessageSquare,\n  Scale,\n  ClipboardList,\n};\n\ninterface DynamicIconProps {\n  name: string;\n  className?: string;\n}\n\nexport default function DynamicIcon({ name, className = \"w-6 h-6\" }: DynamicIconProps) {\n  const IconComponent = iconMap[name as keyof typeof iconMap];\n  \n  if (!IconComponent) {\n    return <div className={className} />;\n  }\n  \n  return <IconComponent className={className} />;\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAWA,MAAM,UAAU;IACd,OAAA,oMAAA,CAAA,QAAK;IACL,SAAA,wMAAA,CAAA,UAAO;IACP,SAAA,4MAAA,CAAA,UAAO;IACP,aAAA,oNAAA,CAAA,cAAW;IACX,KAAA,gMAAA,CAAA,MAAG;IACH,eAAA,wNAAA,CAAA,gBAAa;IACb,OAAA,oMAAA,CAAA,QAAK;IACL,eAAA,wNAAA,CAAA,gBAAa;AACf;AAOe,SAAS,YAAY,EAAE,IAAI,EAAE,YAAY,SAAS,EAAoB;IACnF,MAAM,gBAAgB,OAAO,CAAC,KAA6B;IAE3D,IAAI,CAAC,eAAe;QAClB,qBAAO,8OAAC;YAAI,WAAW;;;;;;IACzB;IAEA,qBAAO,8OAAC;QAAc,WAAW;;;;;;AACnC", "debugId": null}}, {"offset": {"line": 637, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/vscode/%E6%96%B0%E9%97%BB%E8%87%AA%E7%94%B1/baziforum/src/components/SectionContent.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/SectionContent.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/SectionContent.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAqS,GAClU,mEACA", "debugId": null}}, {"offset": {"line": 651, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/vscode/%E6%96%B0%E9%97%BB%E8%87%AA%E7%94%B1/baziforum/src/components/SectionContent.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/SectionContent.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/SectionContent.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAiR,GAC9S,+CACA", "debugId": null}}, {"offset": {"line": 665, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 675, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/vscode/%E6%96%B0%E9%97%BB%E8%87%AA%E7%94%B1/baziforum/src/app/%5Bslug%5D/page.tsx"], "sourcesContent": ["import Link from 'next/link';\nimport { notFound } from 'next/navigation';\nimport { baziSections } from '@/data/sections';\nimport { samplePosts } from '@/data/posts';\nimport DynamicIcon from '@/components/DynamicIcon';\nimport SectionContent from '@/components/SectionContent';\nimport { ArrowLeft, Plus } from 'lucide-react';\n\nexport async function generateStaticParams() {\n  return baziSections.map((section) => ({\n    slug: section.slug,\n  }));\n}\n\ninterface SectionPageProps {\n  params: Promise<{\n    slug: string;\n  }>;\n}\n\nexport default async function SectionPage({ params }: SectionPageProps) {\n  const { slug } = await params;\n\n  const section = baziSections.find(s => s.slug === slug);\n  if (!section) {\n    notFound();\n  }\n\n  // 过滤当前板块的帖子\n  const sectionPosts = samplePosts.filter(post => post.category === section.key);\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white border-b border-gray-200 sticky top-0 z-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between h-16\">\n            <div className=\"flex items-center space-x-4\">\n              <Link href=\"/\" className=\"flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors\">\n                <ArrowLeft className=\"w-5 h-5\" />\n                <span>返回首页</span>\n              </Link>\n              <div className=\"h-6 w-px bg-gray-300\" />\n              <div className=\"flex items-center space-x-3\">\n                <DynamicIcon \n                  name={section.icon} \n                  className=\"w-8 h-8 text-gray-600\" \n                />\n                <div>\n                  <h1 className=\"text-xl font-bold text-gray-900\">{section.name}</h1>\n                  <p className=\"text-sm text-gray-600\">{section.description}</p>\n                </div>\n              </div>\n            </div>\n            \n            <Link href={`/${section.slug}/new`} className=\"btn-primary flex items-center space-x-2\">\n              <Plus className=\"w-4 h-4\" />\n              <span>发帖</span>\n            </Link>\n          </div>\n        </div>\n      </header>\n\n      <SectionContent section={section} posts={sectionPosts} />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;;;;;;;;;AAEO,eAAe;IACpB,OAAO,uHAAA,CAAA,eAAY,CAAC,GAAG,CAAC,CAAC,UAAY,CAAC;YACpC,MAAM,QAAQ,IAAI;QACpB,CAAC;AACH;AAQe,eAAe,YAAY,EAAE,MAAM,EAAoB;IACpE,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM;IAEvB,MAAM,UAAU,uHAAA,CAAA,eAAY,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK;IAClD,IAAI,CAAC,SAAS;QACZ,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IACT;IAEA,YAAY;IACZ,MAAM,eAAe,oHAAA,CAAA,cAAW,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK,QAAQ,GAAG;IAE7E,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;;0DACvB,8OAAC,gNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,UAAW;gDACV,MAAM,QAAQ,IAAI;gDAClB,WAAU;;;;;;0DAEZ,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAmC,QAAQ,IAAI;;;;;;kEAC7D,8OAAC;wDAAE,WAAU;kEAAyB,QAAQ,WAAW;;;;;;;;;;;;;;;;;;;;;;;;0CAK/D,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAM,CAAC,CAAC,EAAE,QAAQ,IAAI,CAAC,IAAI,CAAC;gCAAE,WAAU;;kDAC5C,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAMd,8OAAC,oIAAA,CAAA,UAAc;gBAAC,SAAS;gBAAS,OAAO;;;;;;;;;;;;AAG/C", "debugId": null}}]}