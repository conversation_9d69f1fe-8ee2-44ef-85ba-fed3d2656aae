{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/vscode/%E6%96%B0%E9%97%BB%E8%87%AA%E7%94%B1/baziforum/src/data/sections.ts"], "sourcesContent": ["import { BaziSection } from '@/types';\n\nexport const baziSections: BaziSection[] = [\n  {\n    key: '新',\n    name: '最新事件',\n    description: '浏览当前社会热点内容，作为热榜入口',\n    color: 'bg-red-500',\n    icon: 'Flame'\n  },\n  {\n    key: '闻',\n    name: '重大事件档案',\n    description: '汇总关键事件、转折记录，构成\"公共记忆\"',\n    color: 'bg-blue-500',\n    icon: 'Archive'\n  },\n  {\n    key: '自',\n    name: '个人观点发表',\n    description: '自由表达区，含日志、原创内容等',\n    color: 'bg-green-500',\n    icon: 'PenTool'\n  },\n  {\n    key: '由',\n    name: '假新闻溯源',\n    description: '提供谣言分析、信息溯源内容，引导辟谣意识',\n    color: 'bg-yellow-500',\n    icon: 'SearchCheck'\n  },\n  {\n    key: '舆',\n    name: '媒体观察',\n    description: '分析媒体偏向、舆论塑造机制，促思辨',\n    color: 'bg-purple-500',\n    icon: 'Eye'\n  },\n  {\n    key: '论',\n    name: '话题辩论场',\n    description: '开设辩题，设立正反立场，引导参与讨论',\n    color: 'bg-indigo-500',\n    icon: 'MessageSquare'\n  },\n  {\n    key: '监',\n    name: '举报与公审',\n    description: '用户举报展示，管理员与用户共同评价',\n    color: 'bg-orange-500',\n    icon: 'Scale'\n  },\n  {\n    key: '督',\n    name: '社区进展公告',\n    description: '开发日志、任务列表、人员招募、制度建设',\n    color: 'bg-gray-500',\n    icon: 'ClipboardList'\n  }\n];\n"], "names": [], "mappings": ";;;AAEO,MAAM,eAA8B;IACzC;QACE,KAAK;QACL,MAAM;QACN,aAAa;QACb,OAAO;QACP,MAAM;IACR;IACA;QACE,KAAK;QACL,MAAM;QACN,aAAa;QACb,OAAO;QACP,MAAM;IACR;IACA;QACE,KAAK;QACL,MAAM;QACN,aAAa;QACb,OAAO;QACP,MAAM;IACR;IACA;QACE,KAAK;QACL,MAAM;QACN,aAAa;QACb,OAAO;QACP,MAAM;IACR;IACA;QACE,KAAK;QACL,MAAM;QACN,aAAa;QACb,OAAO;QACP,MAAM;IACR;IACA;QACE,KAAK;QACL,MAAM;QACN,aAAa;QACb,OAAO;QACP,MAAM;IACR;IACA;QACE,KAAK;QACL,MAAM;QACN,aAAa;QACb,OAAO;QACP,MAAM;IACR;IACA;QACE,KAAK;QACL,MAAM;QACN,aAAa;QACb,OAAO;QACP,MAAM;IACR;CACD", "debugId": null}}, {"offset": {"line": 97, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/vscode/%E6%96%B0%E9%97%BB%E8%87%AA%E7%94%B1/baziforum/src/data/posts.ts"], "sourcesContent": ["import { Post } from '@/types';\n\nexport const samplePosts: Post[] = [\n  // 新 - 最新事件\n  {\n    id: '1',\n    title: '高校学生举报教授学术不端事件持续发酵',\n    content: '近日，某知名高校学生实名举报导师学术不端的事件在网络上引起广泛关注。该学生通过社交媒体详细披露了导师在学术研究中存在的数据造假、剽窃他人成果等问题...',\n    excerpt: '学生实名举报导师学术不端，引发社会对高校学术诚信的广泛讨论',\n    author: '新闻观察员',\n    category: '新',\n    createdAt: '2024-01-15T10:30:00Z',\n    updatedAt: '2024-01-15T10:30:00Z',\n    likes: 156,\n    comments: 23,\n    views: 1205,\n    tags: ['学术诚信', '高校', '举报'],\n    isHot: true\n  },\n  {\n    id: '2',\n    title: '某地环保数据造假案件调查进展',\n    content: '环保部门数据造假案件调查取得重要进展，多名相关责任人被立案调查。此案暴露出环境监测数据造假的严重问题...',\n    excerpt: '环保数据造假案调查进展，多人被立案调查',\n    author: '环保关注者',\n    category: '新',\n    createdAt: '2024-01-14T15:20:00Z',\n    updatedAt: '2024-01-14T15:20:00Z',\n    likes: 89,\n    comments: 15,\n    views: 756,\n    tags: ['环保', '数据造假', '调查']\n  },\n\n  // 闻 - 重大事件档案\n  {\n    id: '3',\n    title: '回顾：2023年度十大新闻自由事件',\n    content: '2023年是新闻自由面临重大挑战的一年。从记者采访权受限到媒体报道空间收窄，我们见证了多起影响深远的事件...',\n    excerpt: '盘点2023年影响新闻自由的重大事件及其深远影响',\n    author: '媒体研究者',\n    category: '闻',\n    createdAt: '2024-01-10T09:00:00Z',\n    updatedAt: '2024-01-10T09:00:00Z',\n    likes: 234,\n    comments: 45,\n    views: 2103,\n    tags: ['新闻自由', '年度盘点', '媒体'],\n    isSticky: true\n  },\n\n  // 自 - 个人观点发表\n  {\n    id: '4',\n    title: '作为大学生，我们为什么需要关注新闻自由',\n    content: '很多同学觉得新闻自由离我们很远，但实际上它与我们的日常生活息息相关。当我们无法获得真实信息时，我们的判断就会出现偏差...',\n    excerpt: '一名大学生对新闻自由重要性的思考和感悟',\n    author: '思考中的学生',\n    category: '自',\n    createdAt: '2024-01-13T20:15:00Z',\n    updatedAt: '2024-01-13T20:15:00Z',\n    likes: 67,\n    comments: 12,\n    views: 445,\n    tags: ['大学生', '思考', '新闻自由']\n  },\n\n  // 由 - 假新闻溯源\n  {\n    id: '5',\n    title: '如何识别和应对网络谣言：一份实用指南',\n    content: '在信息爆炸的时代，谣言传播速度极快。本文将教你如何通过多种方法验证信息真实性，包括查证消息源、交叉验证等技巧...',\n    excerpt: '教你如何在信息时代识别和应对网络谣言的实用方法',\n    author: '事实核查员',\n    category: '由',\n    createdAt: '2024-01-12T14:30:00Z',\n    updatedAt: '2024-01-12T14:30:00Z',\n    likes: 145,\n    comments: 28,\n    views: 892,\n    tags: ['谣言识别', '事实核查', '媒体素养']\n  },\n\n  // 舆 - 媒体观察\n  {\n    id: '6',\n    title: '社交媒体算法如何影响我们的信息获取',\n    content: '社交媒体平台的推荐算法正在深刻影响着我们接收信息的方式。算法会根据我们的行为偏好推送内容，这可能导致信息茧房效应...',\n    excerpt: '分析社交媒体算法对信息传播和公众认知的影响',\n    author: '算法观察者',\n    category: '舆',\n    createdAt: '2024-01-11T16:45:00Z',\n    updatedAt: '2024-01-11T16:45:00Z',\n    likes: 198,\n    comments: 34,\n    views: 1156,\n    tags: ['算法', '信息茧房', '社交媒体']\n  },\n\n  // 论 - 话题辩论场\n  {\n    id: '7',\n    title: '辩论：网络实名制是否有利于净化网络环境？',\n    content: '正方观点：网络实名制能够有效减少网络暴力和虚假信息传播，提高网络言论质量。反方观点：实名制可能限制言论自由，让人们不敢表达真实想法...',\n    excerpt: '关于网络实名制利弊的深度辩论，欢迎参与讨论',\n    author: '辩论主持人',\n    category: '论',\n    createdAt: '2024-01-09T11:20:00Z',\n    updatedAt: '2024-01-09T11:20:00Z',\n    likes: 89,\n    comments: 67,\n    views: 1334,\n    tags: ['网络实名制', '言论自由', '辩论']\n  },\n\n  // 监 - 举报与公审\n  {\n    id: '8',\n    title: '用户举报：某自媒体传播不实信息案例分析',\n    content: '近期收到用户举报，某自媒体账号发布了关于疫苗安全性的不实信息。经过调查核实，该信息确实存在误导性内容...',\n    excerpt: '用户举报自媒体传播不实信息的案例分析和处理结果',\n    author: '社区管理员',\n    category: '监',\n    createdAt: '2024-01-08T13:10:00Z',\n    updatedAt: '2024-01-08T13:10:00Z',\n    likes: 76,\n    comments: 19,\n    views: 623,\n    tags: ['举报', '不实信息', '自媒体']\n  },\n\n  // 督 - 社区进展公告\n  {\n    id: '9',\n    title: '八字论坛开发进展报告 - 2024年1月',\n    content: '本月开发进展：完成了基础框架搭建，实现了八个板块的基本功能，优化了移动端体验。下月计划：增加用户系统，完善评论功能...',\n    excerpt: '八字论坛项目最新开发进展和下阶段计划',\n    author: '项目负责人',\n    category: '督',\n    createdAt: '2024-01-07T10:00:00Z',\n    updatedAt: '2024-01-07T10:00:00Z',\n    likes: 45,\n    comments: 8,\n    views: 234,\n    tags: ['开发进展', '项目更新', '社区建设'],\n    isSticky: true\n  }\n];\n"], "names": [], "mappings": ";;;AAEO,MAAM,cAAsB;IACjC,WAAW;IACX;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,UAAU;QACV,WAAW;QACX,WAAW;QACX,OAAO;QACP,UAAU;QACV,OAAO;QACP,MAAM;YAAC;YAAQ;YAAM;SAAK;QAC1B,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,UAAU;QACV,WAAW;QACX,WAAW;QACX,OAAO;QACP,UAAU;QACV,OAAO;QACP,MAAM;YAAC;YAAM;YAAQ;SAAK;IAC5B;IAEA,aAAa;IACb;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,UAAU;QACV,WAAW;QACX,WAAW;QACX,OAAO;QACP,UAAU;QACV,OAAO;QACP,MAAM;YAAC;YAAQ;YAAQ;SAAK;QAC5B,UAAU;IACZ;IAEA,aAAa;IACb;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,UAAU;QACV,WAAW;QACX,WAAW;QACX,OAAO;QACP,UAAU;QACV,OAAO;QACP,MAAM;YAAC;YAAO;YAAM;SAAO;IAC7B;IAEA,YAAY;IACZ;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,UAAU;QACV,WAAW;QACX,WAAW;QACX,OAAO;QACP,UAAU;QACV,OAAO;QACP,MAAM;YAAC;YAAQ;YAAQ;SAAO;IAChC;IAEA,WAAW;IACX;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,UAAU;QACV,WAAW;QACX,WAAW;QACX,OAAO;QACP,UAAU;QACV,OAAO;QACP,MAAM;YAAC;YAAM;YAAQ;SAAO;IAC9B;IAEA,YAAY;IACZ;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,UAAU;QACV,WAAW;QACX,WAAW;QACX,OAAO;QACP,UAAU;QACV,OAAO;QACP,MAAM;YAAC;YAAS;YAAQ;SAAK;IAC/B;IAEA,YAAY;IACZ;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,UAAU;QACV,WAAW;QACX,WAAW;QACX,OAAO;QACP,UAAU;QACV,OAAO;QACP,MAAM;YAAC;YAAM;YAAQ;SAAM;IAC7B;IAEA,aAAa;IACb;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,UAAU;QACV,WAAW;QACX,WAAW;QACX,OAAO;QACP,UAAU;QACV,OAAO;QACP,MAAM;YAAC;YAAQ;YAAQ;SAAO;QAC9B,UAAU;IACZ;CACD", "debugId": null}}, {"offset": {"line": 281, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/vscode/%E6%96%B0%E9%97%BB%E8%87%AA%E7%94%B1/baziforum/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\n\nexport function cn(...inputs: ClassValue[]) {\n  return clsx(inputs);\n}\n\nexport function formatDate(dateString: string): string {\n  const date = new Date(dateString);\n  const now = new Date();\n  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n\n  if (diffInSeconds < 60) {\n    return '刚刚';\n  } else if (diffInSeconds < 3600) {\n    const minutes = Math.floor(diffInSeconds / 60);\n    return `${minutes}分钟前`;\n  } else if (diffInSeconds < 86400) {\n    const hours = Math.floor(diffInSeconds / 3600);\n    return `${hours}小时前`;\n  } else if (diffInSeconds < 2592000) {\n    const days = Math.floor(diffInSeconds / 86400);\n    return `${days}天前`;\n  } else {\n    return date.toLocaleDateString('zh-CN');\n  }\n}\n\nexport function formatNumber(num: number): string {\n  if (num < 1000) {\n    return num.toString();\n  } else if (num < 10000) {\n    return `${(num / 1000).toFixed(1)}k`;\n  } else {\n    return `${(num / 10000).toFixed(1)}w`;\n  }\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) {\n    return text;\n  }\n  return text.slice(0, maxLength) + '...';\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACd;AAEO,SAAS,WAAW,UAAkB;IAC3C,MAAM,OAAO,IAAI,KAAK;IACtB,MAAM,MAAM,IAAI;IAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI;IAEpE,IAAI,gBAAgB,IAAI;QACtB,OAAO;IACT,OAAO,IAAI,gBAAgB,MAAM;QAC/B,MAAM,UAAU,KAAK,KAAK,CAAC,gBAAgB;QAC3C,OAAO,GAAG,QAAQ,GAAG,CAAC;IACxB,OAAO,IAAI,gBAAgB,OAAO;QAChC,MAAM,QAAQ,KAAK,KAAK,CAAC,gBAAgB;QACzC,OAAO,GAAG,MAAM,GAAG,CAAC;IACtB,OAAO,IAAI,gBAAgB,SAAS;QAClC,MAAM,OAAO,KAAK,KAAK,CAAC,gBAAgB;QACxC,OAAO,GAAG,KAAK,EAAE,CAAC;IACpB,OAAO;QACL,OAAO,KAAK,kBAAkB,CAAC;IACjC;AACF;AAEO,SAAS,aAAa,GAAW;IACtC,IAAI,MAAM,MAAM;QACd,OAAO,IAAI,QAAQ;IACrB,OAAO,IAAI,MAAM,OAAO;QACtB,OAAO,GAAG,CAAC,MAAM,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IACtC,OAAO;QACL,OAAO,GAAG,CAAC,MAAM,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IACvC;AACF;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW;QAC5B,OAAO;IACT;IACA,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC", "debugId": null}}, {"offset": {"line": 332, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/vscode/%E6%96%B0%E9%97%BB%E8%87%AA%E7%94%B1/baziforum/src/components/PostCard.tsx"], "sourcesContent": ["import Link from 'next/link';\nimport { Post } from '@/types';\nimport { formatDate, formatNumber } from '@/lib/utils';\nimport { Heart, MessageCircle, Eye, Pin } from 'lucide-react';\n\ninterface PostCardProps {\n  post: Post;\n}\n\nexport default function PostCard({ post }: PostCardProps) {\n  return (\n    <div className=\"card hover:shadow-md transition-shadow\">\n      <div className=\"flex items-start justify-between mb-3\">\n        <div className=\"flex items-center space-x-2\">\n          <span className={`px-2 py-1 text-xs font-medium rounded-full ${\n            post.category === '新' ? 'bg-red-100 text-red-700' :\n            post.category === '闻' ? 'bg-blue-100 text-blue-700' :\n            post.category === '自' ? 'bg-green-100 text-green-700' :\n            post.category === '由' ? 'bg-yellow-100 text-yellow-700' :\n            post.category === '舆' ? 'bg-purple-100 text-purple-700' :\n            post.category === '论' ? 'bg-indigo-100 text-indigo-700' :\n            post.category === '监' ? 'bg-orange-100 text-orange-700' :\n            'bg-gray-100 text-gray-700'\n          }`}>\n            {post.category}\n          </span>\n          {post.isSticky && (\n            <Pin className=\"w-4 h-4 text-orange-500\" />\n          )}\n          {post.isHot && (\n            <span className=\"text-red-500 text-xs\">🔥</span>\n          )}\n        </div>\n        <span className=\"text-xs text-gray-500\">{formatDate(post.createdAt)}</span>\n      </div>\n      \n      <Link href={`/post/${post.id}`} className=\"block group\">\n        <h3 className=\"font-semibold text-gray-900 group-hover:text-blue-600 transition-colors mb-2 line-clamp-2\">\n          {post.title}\n        </h3>\n        <p className=\"text-gray-600 text-sm mb-3 line-clamp-2\">\n          {post.excerpt}\n        </p>\n      </Link>\n      \n      <div className=\"flex items-center justify-between text-sm text-gray-500\">\n        <div className=\"flex items-center space-x-1\">\n          <span className=\"font-medium\">{post.author}</span>\n        </div>\n        \n        <div className=\"flex items-center space-x-4\">\n          <div className=\"flex items-center space-x-1\">\n            <Heart className=\"w-4 h-4\" />\n            <span>{formatNumber(post.likes)}</span>\n          </div>\n          <div className=\"flex items-center space-x-1\">\n            <MessageCircle className=\"w-4 h-4\" />\n            <span>{formatNumber(post.comments)}</span>\n          </div>\n          <div className=\"flex items-center space-x-1\">\n            <Eye className=\"w-4 h-4\" />\n            <span>{formatNumber(post.views)}</span>\n          </div>\n        </div>\n      </div>\n      \n      {post.tags.length > 0 && (\n        <div className=\"flex flex-wrap gap-1 mt-3\">\n          {post.tags.slice(0, 3).map((tag) => (\n            <span\n              key={tag}\n              className=\"px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded\"\n            >\n              #{tag}\n            </span>\n          ))}\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AACA;AAAA;AAAA;AAAA;;;;;AAMe,SAAS,SAAS,EAAE,IAAI,EAAiB;IACtD,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAW,CAAC,2CAA2C,EAC3D,KAAK,QAAQ,KAAK,MAAM,4BACxB,KAAK,QAAQ,KAAK,MAAM,8BACxB,KAAK,QAAQ,KAAK,MAAM,gCACxB,KAAK,QAAQ,KAAK,MAAM,kCACxB,KAAK,QAAQ,KAAK,MAAM,kCACxB,KAAK,QAAQ,KAAK,MAAM,kCACxB,KAAK,QAAQ,KAAK,MAAM,kCACxB,6BACA;0CACC,KAAK,QAAQ;;;;;;4BAEf,KAAK,QAAQ,kBACZ,8OAAC,gMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;4BAEhB,KAAK,KAAK,kBACT,8OAAC;gCAAK,WAAU;0CAAuB;;;;;;;;;;;;kCAG3C,8OAAC;wBAAK,WAAU;kCAAyB,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,SAAS;;;;;;;;;;;;0BAGpE,8OAAC,4JAAA,CAAA,UAAI;gBAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gBAAE,WAAU;;kCACxC,8OAAC;wBAAG,WAAU;kCACX,KAAK,KAAK;;;;;;kCAEb,8OAAC;wBAAE,WAAU;kCACV,KAAK,OAAO;;;;;;;;;;;;0BAIjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAK,WAAU;sCAAe,KAAK,MAAM;;;;;;;;;;;kCAG5C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;kDAAM,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,KAAK,KAAK;;;;;;;;;;;;0CAEhC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,wNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;kDACzB,8OAAC;kDAAM,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,KAAK,QAAQ;;;;;;;;;;;;0CAEnC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,gMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;kDACf,8OAAC;kDAAM,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;;YAKnC,KAAK,IAAI,CAAC,MAAM,GAAG,mBAClB,8OAAC;gBAAI,WAAU;0BACZ,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,oBAC1B,8OAAC;wBAEC,WAAU;;4BACX;4BACG;;uBAHG;;;;;;;;;;;;;;;;AAUnB", "debugId": null}}, {"offset": {"line": 558, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/vscode/%E6%96%B0%E9%97%BB%E8%87%AA%E7%94%B1/baziforum/src/components/DynamicIcon.tsx"], "sourcesContent": ["import { \n  Flame, \n  Archive, \n  PenTool, \n  SearchCheck, \n  Eye, \n  MessageSquare, \n  Scale, \n  ClipboardList \n} from 'lucide-react';\n\nconst iconMap = {\n  Flame,\n  Archive,\n  PenTool,\n  SearchCheck,\n  Eye,\n  MessageSquare,\n  Scale,\n  ClipboardList,\n};\n\ninterface DynamicIconProps {\n  name: string;\n  className?: string;\n}\n\nexport default function DynamicIcon({ name, className = \"w-6 h-6\" }: DynamicIconProps) {\n  const IconComponent = iconMap[name as keyof typeof iconMap];\n  \n  if (!IconComponent) {\n    return <div className={className} />;\n  }\n  \n  return <IconComponent className={className} />;\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAWA,MAAM,UAAU;IACd,OAAA,oMAAA,CAAA,QAAK;IACL,SAAA,wMAAA,CAAA,UAAO;IACP,SAAA,4MAAA,CAAA,UAAO;IACP,aAAA,oNAAA,CAAA,cAAW;IACX,KAAA,gMAAA,CAAA,MAAG;IACH,eAAA,wNAAA,CAAA,gBAAa;IACb,OAAA,oMAAA,CAAA,QAAK;IACL,eAAA,wNAAA,CAAA,gBAAa;AACf;AAOe,SAAS,YAAY,EAAE,IAAI,EAAE,YAAY,SAAS,EAAoB;IACnF,MAAM,gBAAgB,OAAO,CAAC,KAA6B;IAE3D,IAAI,CAAC,eAAe;QAClB,qBAAO,8OAAC;YAAI,WAAW;;;;;;IACzB;IAEA,qBAAO,8OAAC;QAAc,WAAW;;;;;;AACnC", "debugId": null}}, {"offset": {"line": 607, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/vscode/%E6%96%B0%E9%97%BB%E8%87%AA%E7%94%B1/baziforum/src/app/forum/page.tsx"], "sourcesContent": ["import Link from 'next/link';\nimport { baziSections } from '@/data/sections';\nimport { samplePosts } from '@/data/posts';\nimport PostCard from '@/components/PostCard';\nimport DynamicIcon from '@/components/DynamicIcon';\nimport { ArrowLeft, TrendingUp, Clock, Star } from 'lucide-react';\n\nexport default function ForumPage() {\n  // 获取热门帖子（按点赞数+评论数+浏览数排序）\n  const hotPosts = [...samplePosts]\n    .sort((a, b) => (b.likes + b.comments + b.views) - (a.likes + a.comments + a.views))\n    .slice(0, 5);\n\n  // 获取最新帖子\n  const latestPosts = [...samplePosts]\n    .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())\n    .slice(0, 5);\n\n  // 获取置顶帖子\n  const stickyPosts = samplePosts.filter(post => post.isSticky);\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white border-b border-gray-200 sticky top-0 z-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between h-16\">\n            <div className=\"flex items-center space-x-4\">\n              <Link href=\"/\" className=\"flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors\">\n                <ArrowLeft className=\"w-5 h-5\" />\n                <span>返回首页</span>\n              </Link>\n              <div className=\"h-6 w-px bg-gray-300\" />\n              <h1 className=\"text-xl font-bold text-gray-900\">八字论坛</h1>\n            </div>\n            \n            <nav className=\"hidden md:flex space-x-6\">\n              <Link href=\"/about\" className=\"text-gray-600 hover:text-blue-600 transition-colors\">\n                项目理念\n              </Link>\n              <Link href=\"/join\" className=\"btn-primary\">\n                加入我们\n              </Link>\n            </nav>\n          </div>\n        </div>\n      </header>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"grid lg:grid-cols-4 gap-8\">\n          {/* Main Content */}\n          <div className=\"lg:col-span-3 space-y-8\">\n            {/* Sticky Posts */}\n            {stickyPosts.length > 0 && (\n              <section>\n                <div className=\"flex items-center space-x-2 mb-4\">\n                  <Star className=\"w-5 h-5 text-orange-500\" />\n                  <h2 className=\"text-lg font-semibold text-gray-900\">置顶公告</h2>\n                </div>\n                <div className=\"space-y-4\">\n                  {stickyPosts.map((post) => (\n                    <PostCard key={post.id} post={post} />\n                  ))}\n                </div>\n              </section>\n            )}\n\n            {/* Hot Posts */}\n            <section>\n              <div className=\"flex items-center space-x-2 mb-4\">\n                <TrendingUp className=\"w-5 h-5 text-red-500\" />\n                <h2 className=\"text-lg font-semibold text-gray-900\">热门讨论</h2>\n              </div>\n              <div className=\"space-y-4\">\n                {hotPosts.map((post) => (\n                  <PostCard key={post.id} post={post} />\n                ))}\n              </div>\n            </section>\n\n            {/* Latest Posts */}\n            <section>\n              <div className=\"flex items-center space-x-2 mb-4\">\n                <Clock className=\"w-5 h-5 text-blue-500\" />\n                <h2 className=\"text-lg font-semibold text-gray-900\">最新发布</h2>\n              </div>\n              <div className=\"space-y-4\">\n                {latestPosts.map((post) => (\n                  <PostCard key={post.id} post={post} />\n                ))}\n              </div>\n            </section>\n          </div>\n\n          {/* Sidebar */}\n          <div className=\"space-y-6\">\n            {/* Sections Overview */}\n            <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">板块导航</h3>\n              <div className=\"space-y-3\">\n                {baziSections.map((section) => {\n                  const sectionPostCount = samplePosts.filter(p => p.category === section.key).length;\n                  return (\n                    <Link\n                      key={section.key}\n                      href={`/section/${section.key}`}\n                      className=\"flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors group\"\n                    >\n                      <div className=\"flex items-center space-x-3\">\n                        <DynamicIcon\n                          name={section.icon}\n                          className=\"w-6 h-6 text-gray-600 group-hover:text-blue-600 transition-colors\"\n                        />\n                        <div>\n                          <div className=\"font-medium text-gray-900 group-hover:text-blue-600 transition-colors\">\n                            {section.key} - {section.name}\n                          </div>\n                          <div className=\"text-sm text-gray-500\">\n                            {section.description}\n                          </div>\n                        </div>\n                      </div>\n                      <span className=\"text-sm text-gray-400\">\n                        {sectionPostCount}\n                      </span>\n                    </Link>\n                  );\n                })}\n              </div>\n            </div>\n\n            {/* Quick Stats */}\n            <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">社区统计</h3>\n              <div className=\"space-y-3\">\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">总帖子数</span>\n                  <span className=\"font-medium text-gray-900\">{samplePosts.length}</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">活跃板块</span>\n                  <span className=\"font-medium text-gray-900\">{baziSections.length}</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">今日新增</span>\n                  <span className=\"font-medium text-gray-900\">3</span>\n                </div>\n              </div>\n            </div>\n\n            {/* About */}\n            <div className=\"bg-blue-50 rounded-lg p-6\">\n              <h3 className=\"text-lg font-semibold text-blue-900 mb-3\">关于八字论坛</h3>\n              <p className=\"text-blue-800 text-sm leading-relaxed mb-4\">\n                以&ldquo;新闻自由、舆论监督&rdquo;为核心价值的公共议题讨论平台，致力于推动理性对话与社会进步。\n              </p>\n              <Link href=\"/about\" className=\"text-blue-600 hover:text-blue-700 text-sm font-medium\">\n                了解更多 →\n              </Link>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;;;;;;AAEe,SAAS;IACtB,yBAAyB;IACzB,MAAM,WAAW;WAAI,oHAAA,CAAA,cAAW;KAAC,CAC9B,IAAI,CAAC,CAAC,GAAG,IAAM,AAAC,EAAE,KAAK,GAAG,EAAE,QAAQ,GAAG,EAAE,KAAK,GAAI,CAAC,EAAE,KAAK,GAAG,EAAE,QAAQ,GAAG,EAAE,KAAK,GACjF,KAAK,CAAC,GAAG;IAEZ,SAAS;IACT,MAAM,cAAc;WAAI,oHAAA,CAAA,cAAW;KAAC,CACjC,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,IAC9E,KAAK,CAAC,GAAG;IAEZ,SAAS;IACT,MAAM,cAAc,oHAAA,CAAA,cAAW,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ;IAE5D,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;;0DACvB,8OAAC,gNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAG,WAAU;kDAAkC;;;;;;;;;;;;0CAGlD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAAsD;;;;;;kDAGpF,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAQ,WAAU;kDAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQnD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;gCAEZ,YAAY,MAAM,GAAG,mBACpB,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;oDAAG,WAAU;8DAAsC;;;;;;;;;;;;sDAEtD,8OAAC;4CAAI,WAAU;sDACZ,YAAY,GAAG,CAAC,CAAC,qBAChB,8OAAC,8HAAA,CAAA,UAAQ;oDAAe,MAAM;mDAAf,KAAK,EAAE;;;;;;;;;;;;;;;;8CAO9B,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,8OAAC;oDAAG,WAAU;8DAAsC;;;;;;;;;;;;sDAEtD,8OAAC;4CAAI,WAAU;sDACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,8HAAA,CAAA,UAAQ;oDAAe,MAAM;mDAAf,KAAK,EAAE;;;;;;;;;;;;;;;;8CAM5B,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC;oDAAG,WAAU;8DAAsC;;;;;;;;;;;;sDAEtD,8OAAC;4CAAI,WAAU;sDACZ,YAAY,GAAG,CAAC,CAAC,qBAChB,8OAAC,8HAAA,CAAA,UAAQ;oDAAe,MAAM;mDAAf,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;sCAO9B,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAI,WAAU;sDACZ,uHAAA,CAAA,eAAY,CAAC,GAAG,CAAC,CAAC;gDACjB,MAAM,mBAAmB,oHAAA,CAAA,cAAW,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,QAAQ,GAAG,EAAE,MAAM;gDACnF,qBACE,8OAAC,4JAAA,CAAA,UAAI;oDAEH,MAAM,CAAC,SAAS,EAAE,QAAQ,GAAG,EAAE;oDAC/B,WAAU;;sEAEV,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,iIAAA,CAAA,UAAW;oEACV,MAAM,QAAQ,IAAI;oEAClB,WAAU;;;;;;8EAEZ,8OAAC;;sFACC,8OAAC;4EAAI,WAAU;;gFACZ,QAAQ,GAAG;gFAAC;gFAAI,QAAQ,IAAI;;;;;;;sFAE/B,8OAAC;4EAAI,WAAU;sFACZ,QAAQ,WAAW;;;;;;;;;;;;;;;;;;sEAI1B,8OAAC;4DAAK,WAAU;sEACb;;;;;;;mDAnBE,QAAQ,GAAG;;;;;4CAuBtB;;;;;;;;;;;;8CAKJ,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;sEAA6B,oHAAA,CAAA,cAAW,CAAC,MAAM;;;;;;;;;;;;8DAEjE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;sEAA6B,uHAAA,CAAA,eAAY,CAAC,MAAM;;;;;;;;;;;;8DAElE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;sEAA4B;;;;;;;;;;;;;;;;;;;;;;;;8CAMlD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAA6C;;;;;;sDAG1D,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAS,WAAU;sDAAwD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpG", "debugId": null}}]}