module.exports = {

"[project]/.next-internal/server/app/[slug]/page/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[project]/src/app/favicon.ico.mjs { IMAGE => \"[project]/src/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/favicon.ico.mjs { IMAGE => \"[project]/src/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript)"));
}}),
"[project]/src/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/layout.tsx [app-rsc] (ecmascript)"));
}}),
"[project]/src/app/not-found.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/not-found.tsx [app-rsc] (ecmascript)"));
}}),
"[project]/src/data/sections.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "baziSections": (()=>baziSections)
});
const baziSections = [
    {
        key: '新',
        slug: 'latest',
        name: '最新事件',
        description: '浏览当前社会热点内容，作为热榜入口',
        color: 'bg-red-500',
        icon: 'Flame'
    },
    {
        key: '闻',
        slug: 'archives',
        name: '重大事件档案',
        description: '汇总关键事件、转折记录，构成"公共记忆"',
        color: 'bg-blue-500',
        icon: 'Archive'
    },
    {
        key: '自',
        slug: 'opinions',
        name: '个人观点发表',
        description: '自由表达区，含日志、原创内容等',
        color: 'bg-green-500',
        icon: 'PenTool'
    },
    {
        key: '由',
        slug: 'factcheck',
        name: '假新闻溯源',
        description: '提供谣言分析、信息溯源内容，引导辟谣意识',
        color: 'bg-yellow-500',
        icon: 'SearchCheck'
    },
    {
        key: '舆',
        slug: 'mediawatch',
        name: '媒体观察',
        description: '分析媒体偏向、舆论塑造机制，促思辨',
        color: 'bg-purple-500',
        icon: 'Eye'
    },
    {
        key: '论',
        slug: 'debates',
        name: '话题辩论场',
        description: '开设辩题，设立正反立场，引导参与讨论',
        color: 'bg-indigo-500',
        icon: 'MessageSquare'
    },
    {
        key: '监',
        slug: 'reports',
        name: '举报与公审',
        description: '用户举报展示，管理员与用户共同评价',
        color: 'bg-orange-500',
        icon: 'Scale'
    },
    {
        key: '督',
        slug: 'community',
        name: '社区进展公告',
        description: '开发日志、任务列表、人员招募、制度建设',
        color: 'bg-gray-500',
        icon: 'ClipboardList'
    }
];
}}),
"[project]/src/data/posts.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "samplePosts": (()=>samplePosts)
});
const samplePosts = [
    // 新 - 最新事件
    {
        id: '1',
        title: '高校学生举报教授学术不端事件持续发酵',
        content: '近日，某知名高校学生实名举报导师学术不端的事件在网络上引起广泛关注。该学生通过社交媒体详细披露了导师在学术研究中存在的数据造假、剽窃他人成果等问题...',
        excerpt: '学生实名举报导师学术不端，引发社会对高校学术诚信的广泛讨论',
        author: '新闻观察员',
        category: '新',
        createdAt: '2024-01-15T10:30:00Z',
        updatedAt: '2024-01-15T10:30:00Z',
        likes: 156,
        comments: 23,
        views: 1205,
        tags: [
            '学术诚信',
            '高校',
            '举报'
        ],
        isHot: true
    },
    {
        id: '2',
        title: '某地环保数据造假案件调查进展',
        content: '环保部门数据造假案件调查取得重要进展，多名相关责任人被立案调查。此案暴露出环境监测数据造假的严重问题...',
        excerpt: '环保数据造假案调查进展，多人被立案调查',
        author: '环保关注者',
        category: '新',
        createdAt: '2024-01-14T15:20:00Z',
        updatedAt: '2024-01-14T15:20:00Z',
        likes: 89,
        comments: 15,
        views: 756,
        tags: [
            '环保',
            '数据造假',
            '调查'
        ]
    },
    // 闻 - 重大事件档案
    {
        id: '3',
        title: '回顾：2023年度十大新闻自由事件',
        content: '2023年是新闻自由面临重大挑战的一年。从记者采访权受限到媒体报道空间收窄，我们见证了多起影响深远的事件...',
        excerpt: '盘点2023年影响新闻自由的重大事件及其深远影响',
        author: '媒体研究者',
        category: '闻',
        createdAt: '2024-01-10T09:00:00Z',
        updatedAt: '2024-01-10T09:00:00Z',
        likes: 234,
        comments: 45,
        views: 2103,
        tags: [
            '新闻自由',
            '年度盘点',
            '媒体'
        ],
        isSticky: true
    },
    // 自 - 个人观点发表
    {
        id: '4',
        title: '作为大学生，我们为什么需要关注新闻自由',
        content: '很多同学觉得新闻自由离我们很远，但实际上它与我们的日常生活息息相关。当我们无法获得真实信息时，我们的判断就会出现偏差...',
        excerpt: '一名大学生对新闻自由重要性的思考和感悟',
        author: '思考中的学生',
        category: '自',
        createdAt: '2024-01-13T20:15:00Z',
        updatedAt: '2024-01-13T20:15:00Z',
        likes: 67,
        comments: 12,
        views: 445,
        tags: [
            '大学生',
            '思考',
            '新闻自由'
        ]
    },
    // 由 - 假新闻溯源
    {
        id: '5',
        title: '如何识别和应对网络谣言：一份实用指南',
        content: '在信息爆炸的时代，谣言传播速度极快。本文将教你如何通过多种方法验证信息真实性，包括查证消息源、交叉验证等技巧...',
        excerpt: '教你如何在信息时代识别和应对网络谣言的实用方法',
        author: '事实核查员',
        category: '由',
        createdAt: '2024-01-12T14:30:00Z',
        updatedAt: '2024-01-12T14:30:00Z',
        likes: 145,
        comments: 28,
        views: 892,
        tags: [
            '谣言识别',
            '事实核查',
            '媒体素养'
        ]
    },
    // 舆 - 媒体观察
    {
        id: '6',
        title: '社交媒体算法如何影响我们的信息获取',
        content: '社交媒体平台的推荐算法正在深刻影响着我们接收信息的方式。算法会根据我们的行为偏好推送内容，这可能导致信息茧房效应...',
        excerpt: '分析社交媒体算法对信息传播和公众认知的影响',
        author: '算法观察者',
        category: '舆',
        createdAt: '2024-01-11T16:45:00Z',
        updatedAt: '2024-01-11T16:45:00Z',
        likes: 198,
        comments: 34,
        views: 1156,
        tags: [
            '算法',
            '信息茧房',
            '社交媒体'
        ]
    },
    // 论 - 话题辩论场
    {
        id: '7',
        title: '辩论：网络实名制是否有利于净化网络环境？',
        content: '正方观点：网络实名制能够有效减少网络暴力和虚假信息传播，提高网络言论质量。反方观点：实名制可能限制言论自由，让人们不敢表达真实想法...',
        excerpt: '关于网络实名制利弊的深度辩论，欢迎参与讨论',
        author: '辩论主持人',
        category: '论',
        createdAt: '2024-01-09T11:20:00Z',
        updatedAt: '2024-01-09T11:20:00Z',
        likes: 89,
        comments: 67,
        views: 1334,
        tags: [
            '网络实名制',
            '言论自由',
            '辩论'
        ]
    },
    // 监 - 举报与公审
    {
        id: '8',
        title: '用户举报：某自媒体传播不实信息案例分析',
        content: '近期收到用户举报，某自媒体账号发布了关于疫苗安全性的不实信息。经过调查核实，该信息确实存在误导性内容...',
        excerpt: '用户举报自媒体传播不实信息的案例分析和处理结果',
        author: '社区管理员',
        category: '监',
        createdAt: '2024-01-08T13:10:00Z',
        updatedAt: '2024-01-08T13:10:00Z',
        likes: 76,
        comments: 19,
        views: 623,
        tags: [
            '举报',
            '不实信息',
            '自媒体'
        ]
    },
    // 督 - 社区进展公告
    {
        id: '9',
        title: '八字论坛开发进展报告 - 2024年1月',
        content: '本月开发进展：完成了基础框架搭建，实现了八个板块的基本功能，优化了移动端体验。下月计划：增加用户系统，完善评论功能...',
        excerpt: '八字论坛项目最新开发进展和下阶段计划',
        author: '项目负责人',
        category: '督',
        createdAt: '2024-01-07T10:00:00Z',
        updatedAt: '2024-01-07T10:00:00Z',
        likes: 45,
        comments: 8,
        views: 234,
        tags: [
            '开发进展',
            '项目更新',
            '社区建设'
        ],
        isSticky: true
    },
    // 更多新闻事件
    {
        id: '10',
        title: '某地政府信息公开申请遭拒，律师提起行政诉讼',
        content: '公益律师向某地政府申请公开环境监测数据，遭到拒绝后提起行政诉讼。此案引发对政府信息公开制度执行情况的关注...',
        excerpt: '政府信息公开申请被拒引发行政诉讼，考验信息透明度',
        author: '法律观察员',
        category: '新',
        createdAt: '2024-01-16T14:20:00Z',
        updatedAt: '2024-01-16T14:20:00Z',
        likes: 78,
        comments: 12,
        views: 567,
        tags: [
            '信息公开',
            '行政诉讼',
            '政府透明度'
        ]
    },
    {
        id: '11',
        title: '网络主播传播虚假疫情信息被处罚案例分析',
        content: '某知名网络主播在直播中传播未经证实的疫情信息，被相关部门处罚。此案反映出网络信息传播的责任问题...',
        excerpt: '网络主播传播虚假疫情信息被罚，网络传播责任受关注',
        author: '网络观察者',
        category: '新',
        createdAt: '2024-01-15T16:30:00Z',
        updatedAt: '2024-01-15T16:30:00Z',
        likes: 92,
        comments: 18,
        views: 743,
        tags: [
            '网络主播',
            '虚假信息',
            '疫情'
        ]
    },
    // 更多档案内容
    {
        id: '12',
        title: '回顾：互联网时代的新闻自由里程碑事件',
        content: '从博客兴起到社交媒体普及，从公民记者到自媒体时代，互联网深刻改变了新闻传播格局。本文梳理了互联网时代新闻自由发展的关键节点...',
        excerpt: '梳理互联网时代新闻自由发展的重要历史节点',
        author: '媒体史研究者',
        category: '闻',
        createdAt: '2024-01-14T11:00:00Z',
        updatedAt: '2024-01-14T11:00:00Z',
        likes: 156,
        comments: 24,
        views: 1234,
        tags: [
            '互联网',
            '新闻史',
            '媒体发展'
        ],
        isHot: true
    },
    {
        id: '13',
        title: '档案：历次重大公共卫生事件中的信息公开',
        content: '从SARS到新冠疫情，重大公共卫生事件中的信息公开一直是社会关注焦点。本文整理了历次疫情中信息公开的经验教训...',
        excerpt: '回顾重大疫情中信息公开的历史经验与教训',
        author: '公共卫生观察员',
        category: '闻',
        createdAt: '2024-01-13T09:15:00Z',
        updatedAt: '2024-01-13T09:15:00Z',
        likes: 134,
        comments: 31,
        views: 987,
        tags: [
            '公共卫生',
            '信息公开',
            '疫情'
        ]
    },
    // 更多个人观点
    {
        id: '14',
        title: '从一名新闻系学生的角度看媒体素养教育',
        content: '作为新闻系的学生，我深感媒体素养教育的重要性。在信息爆炸的时代，如何培养批判性思维，如何识别可靠信息源...',
        excerpt: '新闻系学生分享对媒体素养教育重要性的思考',
        author: '新闻系学生',
        category: '自',
        createdAt: '2024-01-16T19:30:00Z',
        updatedAt: '2024-01-16T19:30:00Z',
        likes: 89,
        comments: 15,
        views: 456,
        tags: [
            '媒体素养',
            '新闻教育',
            '批判思维'
        ]
    },
    {
        id: '15',
        title: '我为什么选择成为一名公民记者',
        content: '在传统媒体之外，公民记者正在发挥越来越重要的作用。我分享自己成为公民记者的经历，以及在这个过程中的思考...',
        excerpt: '一位公民记者分享自己的从业经历和思考',
        author: '公民记者小王',
        category: '自',
        createdAt: '2024-01-15T21:45:00Z',
        updatedAt: '2024-01-15T21:45:00Z',
        likes: 67,
        comments: 9,
        views: 378,
        tags: [
            '公民记者',
            '媒体参与',
            '新闻实践'
        ]
    },
    // 更多假新闻溯源
    {
        id: '16',
        title: '深度分析：某健康谣言的传播路径与辟谣过程',
        content: '一条关于某食品致癌的谣言在网络上快速传播，我们追踪了这条谣言的传播路径，分析了辟谣的过程和效果...',
        excerpt: '追踪健康谣言传播路径，分析辟谣机制的有效性',
        author: '谣言追踪者',
        category: '由',
        createdAt: '2024-01-16T13:20:00Z',
        updatedAt: '2024-01-16T13:20:00Z',
        likes: 112,
        comments: 22,
        views: 689,
        tags: [
            '健康谣言',
            '传播路径',
            '辟谣机制'
        ]
    },
    {
        id: '17',
        title: '如何利用开源情报验证网络传言',
        content: '开源情报（OSINT）技术为普通人验证网络信息提供了新的工具。本文介绍几种常用的开源情报验证方法...',
        excerpt: '介绍开源情报技术在信息验证中的应用方法',
        author: 'OSINT研究员',
        category: '由',
        createdAt: '2024-01-14T17:10:00Z',
        updatedAt: '2024-01-14T17:10:00Z',
        likes: 145,
        comments: 18,
        views: 823,
        tags: [
            '开源情报',
            'OSINT',
            '信息验证'
        ]
    },
    // 更多媒体观察
    {
        id: '18',
        title: '短视频平台的信息传播特点与监管挑战',
        content: '短视频平台已成为重要的信息传播渠道，但其传播特点也带来了新的监管挑战。本文分析短视频信息传播的特点...',
        excerpt: '分析短视频平台信息传播特点及其带来的监管挑战',
        author: '新媒体研究者',
        category: '舆',
        createdAt: '2024-01-16T10:30:00Z',
        updatedAt: '2024-01-16T10:30:00Z',
        likes: 98,
        comments: 16,
        views: 567,
        tags: [
            '短视频',
            '信息传播',
            '平台监管'
        ]
    },
    {
        id: '19',
        title: '人工智能时代的新闻生产与伦理思考',
        content: 'AI技术在新闻生产中的应用越来越广泛，从自动写稿到深度伪造，技术进步带来便利的同时也引发伦理担忧...',
        excerpt: '探讨AI技术在新闻领域应用的机遇与伦理挑战',
        author: 'AI伦理研究者',
        category: '舆',
        createdAt: '2024-01-15T14:45:00Z',
        updatedAt: '2024-01-15T14:45:00Z',
        likes: 167,
        comments: 28,
        views: 934,
        tags: [
            '人工智能',
            '新闻伦理',
            '技术影响'
        ],
        isHot: true
    },
    // 更多辩论话题
    {
        id: '20',
        title: '辩论：社交媒体平台是否应该对用户言论承担更多责任？',
        content: '正方：平台作为信息传播的重要渠道，应该承担更多审核责任，防止有害信息传播。反方：过度审核可能导致言论自由受限...',
        excerpt: '关于社交媒体平台言论责任的深度辩论',
        author: '辩论组织者',
        category: '论',
        createdAt: '2024-01-16T15:20:00Z',
        updatedAt: '2024-01-16T15:20:00Z',
        likes: 134,
        comments: 45,
        views: 876,
        tags: [
            '社交媒体',
            '言论责任',
            '平台治理'
        ]
    },
    {
        id: '21',
        title: '辩论：新闻付费墙是促进还是阻碍了信息传播？',
        content: '正方：付费墙保障了新闻机构的收入，有利于高质量新闻生产。反方：付费墙加剧了信息不平等，阻碍了信息的自由流动...',
        excerpt: '关于新闻付费模式利弊的辩论讨论',
        author: '媒体经济观察员',
        category: '论',
        createdAt: '2024-01-15T11:30:00Z',
        updatedAt: '2024-01-15T11:30:00Z',
        likes: 87,
        comments: 33,
        views: 654,
        tags: [
            '付费新闻',
            '媒体经济',
            '信息平等'
        ]
    },
    // 更多举报监督
    {
        id: '22',
        title: '举报：某地方媒体涉嫌收费删帖行为调查',
        content: '接到举报称某地方媒体存在收费删帖行为，经调查核实，确实存在违规操作。现将调查结果公布，并呼吁加强媒体监管...',
        excerpt: '地方媒体收费删帖行为调查结果公布',
        author: '监督委员会',
        category: '监',
        createdAt: '2024-01-16T12:00:00Z',
        updatedAt: '2024-01-16T12:00:00Z',
        likes: 156,
        comments: 27,
        views: 789,
        tags: [
            '收费删帖',
            '媒体监管',
            '新闻伦理'
        ]
    },
    {
        id: '23',
        title: '公众举报：网络大V传播不实金融信息案例',
        content: '某网络大V在社交平台发布不实金融信息，误导投资者决策。经举报和调查，相关部门已对其进行处理...',
        excerpt: '网络大V传播不实金融信息被举报处理',
        author: '金融监督员',
        category: '监',
        createdAt: '2024-01-14T16:15:00Z',
        updatedAt: '2024-01-14T16:15:00Z',
        likes: 98,
        comments: 19,
        views: 543,
        tags: [
            '网络大V',
            '金融信息',
            '投资误导'
        ]
    },
    // 更多社区公告
    {
        id: '24',
        title: '八字论坛用户行为规范更新公告',
        content: '为了维护良好的社区环境，我们更新了用户行为规范。新规范明确了发帖标准、评论要求、举报流程等内容...',
        excerpt: '社区用户行为规范更新，明确发帖和互动标准',
        author: '社区管理员',
        category: '督',
        createdAt: '2024-01-16T09:00:00Z',
        updatedAt: '2024-01-16T09:00:00Z',
        likes: 67,
        comments: 12,
        views: 345,
        tags: [
            '用户规范',
            '社区管理',
            '行为准则'
        ],
        isSticky: true
    },
    {
        id: '25',
        title: '招募：寻找志同道合的内容审核志愿者',
        content: '随着社区规模扩大，我们需要更多志愿者参与内容审核工作。如果你关注新闻自由和舆论监督，欢迎加入我们...',
        excerpt: '招募内容审核志愿者，共同维护社区环境',
        author: '志愿者协调员',
        category: '督',
        createdAt: '2024-01-15T10:30:00Z',
        updatedAt: '2024-01-15T10:30:00Z',
        likes: 89,
        comments: 15,
        views: 432,
        tags: [
            '志愿者招募',
            '内容审核',
            '社区建设'
        ]
    }
];
}}),
"[project]/src/components/DynamicIcon.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>DynamicIcon)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$flame$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__Flame$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/flame.js [app-rsc] (ecmascript) <export default as Flame>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$archive$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__Archive$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/archive.js [app-rsc] (ecmascript) <export default as Archive>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$pen$2d$tool$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__PenTool$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/pen-tool.js [app-rsc] (ecmascript) <export default as PenTool>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$search$2d$check$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__SearchCheck$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/search-check.js [app-rsc] (ecmascript) <export default as SearchCheck>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$eye$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__Eye$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/eye.js [app-rsc] (ecmascript) <export default as Eye>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$message$2d$square$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__MessageSquare$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/message-square.js [app-rsc] (ecmascript) <export default as MessageSquare>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$scale$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__Scale$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/scale.js [app-rsc] (ecmascript) <export default as Scale>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clipboard$2d$list$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__ClipboardList$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/clipboard-list.js [app-rsc] (ecmascript) <export default as ClipboardList>");
;
;
const iconMap = {
    Flame: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$flame$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__Flame$3e$__["Flame"],
    Archive: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$archive$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__Archive$3e$__["Archive"],
    PenTool: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$pen$2d$tool$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__PenTool$3e$__["PenTool"],
    SearchCheck: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$search$2d$check$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__SearchCheck$3e$__["SearchCheck"],
    Eye: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$eye$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__Eye$3e$__["Eye"],
    MessageSquare: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$message$2d$square$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__MessageSquare$3e$__["MessageSquare"],
    Scale: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$scale$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__Scale$3e$__["Scale"],
    ClipboardList: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clipboard$2d$list$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__ClipboardList$3e$__["ClipboardList"]
};
function DynamicIcon({ name, className = "w-6 h-6" }) {
    const IconComponent = iconMap[name];
    if (!IconComponent) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: className
        }, void 0, false, {
            fileName: "[project]/src/components/DynamicIcon.tsx",
            lineNumber: 32,
            columnNumber: 12
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(IconComponent, {
        className: className
    }, void 0, false, {
        fileName: "[project]/src/components/DynamicIcon.tsx",
        lineNumber: 35,
        columnNumber: 10
    }, this);
}
}}),
"[project]/src/components/SectionContent.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/src/components/SectionContent.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/SectionContent.tsx <module evaluation>", "default");
}}),
"[project]/src/components/SectionContent.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/src/components/SectionContent.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/SectionContent.tsx", "default");
}}),
"[project]/src/components/SectionContent.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$SectionContent$2e$tsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/components/SectionContent.tsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$SectionContent$2e$tsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/src/components/SectionContent.tsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$SectionContent$2e$tsx__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/src/app/[slug]/page.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>SectionPage),
    "generateStaticParams": (()=>generateStaticParams)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$api$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/api/navigation.react-server.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/components/navigation.react-server.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$sections$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/data/sections.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$posts$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/data/posts.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$DynamicIcon$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/DynamicIcon.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$SectionContent$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/SectionContent.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$left$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowLeft$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/arrow-left.js [app-rsc] (ecmascript) <export default as ArrowLeft>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$plus$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__Plus$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/plus.js [app-rsc] (ecmascript) <export default as Plus>");
;
;
;
;
;
;
;
;
async function generateStaticParams() {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$sections$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["baziSections"].map((section)=>({
            slug: section.slug
        }));
}
async function SectionPage({ params }) {
    const { slug } = await params;
    const section = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$sections$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["baziSections"].find((s)=>s.slug === slug);
    if (!section) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["notFound"])();
    }
    // 过滤当前板块的帖子
    const sectionPosts = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$posts$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["samplePosts"].filter((post)=>post.category === section.key);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "min-h-screen bg-gray-50",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("header", {
                className: "bg-white border-b border-gray-200 sticky top-0 z-50",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center justify-between h-16",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center space-x-4",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                                        href: "/",
                                        className: "flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$left$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowLeft$3e$__["ArrowLeft"], {
                                                className: "w-5 h-5"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/[slug]/page.tsx",
                                                lineNumber: 40,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                children: "返回首页"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/[slug]/page.tsx",
                                                lineNumber: 41,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/[slug]/page.tsx",
                                        lineNumber: 39,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "h-6 w-px bg-gray-300"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/[slug]/page.tsx",
                                        lineNumber: 43,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center space-x-3",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$DynamicIcon$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                                                name: section.icon,
                                                className: "w-8 h-8 text-gray-600"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/[slug]/page.tsx",
                                                lineNumber: 45,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                                        className: "text-xl font-bold text-gray-900",
                                                        children: section.name
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/[slug]/page.tsx",
                                                        lineNumber: 50,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: "text-sm text-gray-600",
                                                        children: section.description
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/[slug]/page.tsx",
                                                        lineNumber: 51,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/[slug]/page.tsx",
                                                lineNumber: 49,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/[slug]/page.tsx",
                                        lineNumber: 44,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/[slug]/page.tsx",
                                lineNumber: 38,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                                href: `/${section.slug}/new`,
                                className: "btn-primary flex items-center space-x-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$plus$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__Plus$3e$__["Plus"], {
                                        className: "w-4 h-4"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/[slug]/page.tsx",
                                        lineNumber: 57,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: "发帖"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/[slug]/page.tsx",
                                        lineNumber: 58,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/[slug]/page.tsx",
                                lineNumber: 56,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/[slug]/page.tsx",
                        lineNumber: 37,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/app/[slug]/page.tsx",
                    lineNumber: 36,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/[slug]/page.tsx",
                lineNumber: 35,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$SectionContent$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                section: section,
                posts: sectionPosts
            }, void 0, false, {
                fileName: "[project]/src/app/[slug]/page.tsx",
                lineNumber: 64,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/[slug]/page.tsx",
        lineNumber: 33,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/app/[slug]/page.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/[slug]/page.tsx [app-rsc] (ecmascript)"));
}}),

};

//# sourceMappingURL=_d9482138._.js.map