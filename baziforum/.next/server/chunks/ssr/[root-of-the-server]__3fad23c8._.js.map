{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/vscode/%E6%96%B0%E9%97%BB%E8%87%AA%E7%94%B1/baziforum/src/app/join/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { ArrowLeft, Code, PenTool, Users, ExternalLink } from 'lucide-react';\nimport { JoinFormData } from '@/types';\n\nexport default function JoinPage() {\n  const [formData, setFormData] = useState<JoinFormData>({\n    name: '',\n    email: '',\n    role: 'developer',\n    message: '',\n    skills: [],\n    experience: ''\n  });\n\n  const [submitted, setSubmitted] = useState(false);\n\n  const roles = [\n    {\n      key: 'developer' as const,\n      title: '前端开发',\n      description: 'React/Next.js方向，负责平台功能开发',\n      icon: Code,\n      skills: ['React', 'Next.js', 'TypeScript', 'Tailwind CSS']\n    },\n    {\n      key: 'content' as const,\n      title: '内容策划',\n      description: '公共议题关注者，负责内容策划与写作',\n      icon: PenTool,\n      skills: ['新闻写作', '议题分析', '内容策划', '社会观察']\n    },\n    {\n      key: 'volunteer' as const,\n      title: '社群志愿者',\n      description: '协助社区运营、用户支持等工作',\n      icon: Users,\n      skills: ['社区运营', '用户支持', '活动策划', '沟通协调']\n    }\n  ];\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    // 这里应该提交到后端\n    console.log('Form submitted:', formData);\n    setSubmitted(true);\n  };\n\n  const handleSkillToggle = (skill: string) => {\n    setFormData(prev => ({\n      ...prev,\n      skills: prev.skills?.includes(skill)\n        ? prev.skills.filter(s => s !== skill)\n        : [...(prev.skills || []), skill]\n    }));\n  };\n\n  if (submitted) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"max-w-md mx-auto bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center\">\n          <div className=\"text-green-500 text-6xl mb-4\">✓</div>\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">申请已提交</h2>\n          <p className=\"text-gray-600 mb-6\">\n            感谢您的申请！我们会在3个工作日内通过邮件与您联系。\n          </p>\n          <div className=\"space-y-3\">\n            <Link href=\"/\" className=\"btn-primary w-full\">\n              返回首页\n            </Link>\n            <Link href=\"/about\" className=\"btn-secondary w-full\">\n              了解更多\n            </Link>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white border-b border-gray-200\">\n        <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center h-16\">\n            <Link href=\"/\" className=\"flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors\">\n              <ArrowLeft className=\"w-5 h-5\" />\n              <span>返回首页</span>\n            </Link>\n          </div>\n        </div>\n      </header>\n\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"text-center mb-12\">\n          <h1 className=\"text-4xl font-bold text-gray-900 mb-4\">加入我们</h1>\n          <p className=\"text-xl text-gray-600\">\n            一起推动新闻自由与舆论监督的发展\n          </p>\n        </div>\n\n        <div className=\"grid lg:grid-cols-3 gap-8 mb-12\">\n          {roles.map((role) => {\n            const Icon = role.icon;\n            return (\n              <div\n                key={role.key}\n                className={`card cursor-pointer transition-all ${\n                  formData.role === role.key \n                    ? 'ring-2 ring-blue-500 bg-blue-50' \n                    : 'hover:shadow-md'\n                }`}\n                onClick={() => setFormData(prev => ({ ...prev, role: role.key }))}\n              >\n                <div className=\"text-center\">\n                  <Icon className=\"w-12 h-12 mx-auto mb-4 text-blue-600\" />\n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">{role.title}</h3>\n                  <p className=\"text-gray-600 text-sm mb-4\">{role.description}</p>\n                  <div className=\"flex flex-wrap gap-1 justify-center\">\n                    {role.skills.map((skill) => (\n                      <span\n                        key={skill}\n                        className=\"px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded\"\n                      >\n                        {skill}\n                      </span>\n                    ))}\n                  </div>\n                </div>\n              </div>\n            );\n          })}\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-8\">\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">申请表单</h2>\n          \n          <form onSubmit={handleSubmit} className=\"space-y-6\">\n            <div className=\"grid md:grid-cols-2 gap-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  姓名 *\n                </label>\n                <input\n                  type=\"text\"\n                  required\n                  value={formData.name}\n                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  placeholder=\"请输入您的姓名\"\n                />\n              </div>\n              \n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  邮箱 *\n                </label>\n                <input\n                  type=\"email\"\n                  required\n                  value={formData.email}\n                  onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  placeholder=\"请输入您的邮箱\"\n                />\n              </div>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                相关技能\n              </label>\n              <div className=\"flex flex-wrap gap-2\">\n                {roles.find(r => r.key === formData.role)?.skills.map((skill) => (\n                  <button\n                    key={skill}\n                    type=\"button\"\n                    onClick={() => handleSkillToggle(skill)}\n                    className={`px-3 py-1 text-sm rounded-full transition-colors ${\n                      formData.skills?.includes(skill)\n                        ? 'bg-blue-100 text-blue-700 border border-blue-300'\n                        : 'bg-gray-100 text-gray-600 border border-gray-300 hover:bg-gray-200'\n                    }`}\n                  >\n                    {skill}\n                  </button>\n                ))}\n              </div>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                相关经验\n              </label>\n              <textarea\n                value={formData.experience}\n                onChange={(e) => setFormData(prev => ({ ...prev, experience: e.target.value }))}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                rows={3}\n                placeholder=\"请简述您的相关经验（可选）\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                申请理由 *\n              </label>\n              <textarea\n                required\n                value={formData.message}\n                onChange={(e) => setFormData(prev => ({ ...prev, message: e.target.value }))}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                rows={4}\n                placeholder=\"请告诉我们您为什么想要加入我们，以及您希望为项目贡献什么...\"\n              />\n            </div>\n\n            <div className=\"flex flex-col sm:flex-row gap-4\">\n              <button\n                type=\"submit\"\n                className=\"btn-primary flex-1\"\n              >\n                提交申请\n              </button>\n              \n              <a\n                href=\"https://forms.gle/example\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"btn-secondary flex-1 flex items-center justify-center space-x-2\"\n              >\n                <span>Google Forms</span>\n                <ExternalLink className=\"w-4 h-4\" />\n              </a>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAOe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;QACrD,MAAM;QACN,OAAO;QACP,MAAM;QACN,SAAS;QACT,QAAQ,EAAE;QACV,YAAY;IACd;IAEA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,QAAQ;QACZ;YACE,KAAK;YACL,OAAO;YACP,aAAa;YACb,MAAM,kMAAA,CAAA,OAAI;YACV,QAAQ;gBAAC;gBAAS;gBAAW;gBAAc;aAAe;QAC5D;QACA;YACE,KAAK;YACL,OAAO;YACP,aAAa;YACb,MAAM,4MAAA,CAAA,UAAO;YACb,QAAQ;gBAAC;gBAAQ;gBAAQ;gBAAQ;aAAO;QAC1C;QACA;YACE,KAAK;YACL,OAAO;YACP,aAAa;YACb,MAAM,oMAAA,CAAA,QAAK;YACX,QAAQ;gBAAC;gBAAQ;gBAAQ;gBAAQ;aAAO;QAC1C;KACD;IAED,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,YAAY;QACZ,QAAQ,GAAG,CAAC,mBAAmB;QAC/B,aAAa;IACf;IAEA,MAAM,oBAAoB,CAAC;QACzB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,QAAQ,KAAK,MAAM,EAAE,SAAS,SAC1B,KAAK,MAAM,CAAC,MAAM,CAAC,CAAA,IAAK,MAAM,SAC9B;uBAAK,KAAK,MAAM,IAAI,EAAE;oBAAG;iBAAM;YACrC,CAAC;IACH;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAA+B;;;;;;kCAC9C,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAGlC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CAAqB;;;;;;0CAG9C,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAS,WAAU;0CAAuB;;;;;;;;;;;;;;;;;;;;;;;IAO/D;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;8CACrB,8OAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAMd,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAKvC,8OAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC;4BACV,MAAM,OAAO,KAAK,IAAI;4BACtB,qBACE,8OAAC;gCAEC,WAAW,CAAC,mCAAmC,EAC7C,SAAS,IAAI,KAAK,KAAK,GAAG,GACtB,oCACA,mBACJ;gCACF,SAAS,IAAM,YAAY,CAAA,OAAQ,CAAC;4CAAE,GAAG,IAAI;4CAAE,MAAM,KAAK,GAAG;wCAAC,CAAC;0CAE/D,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;;;;;;sDAChB,8OAAC;4CAAG,WAAU;sDAA4C,KAAK,KAAK;;;;;;sDACpE,8OAAC;4CAAE,WAAU;sDAA8B,KAAK,WAAW;;;;;;sDAC3D,8OAAC;4CAAI,WAAU;sDACZ,KAAK,MAAM,CAAC,GAAG,CAAC,CAAC,sBAChB,8OAAC;oDAEC,WAAU;8DAET;mDAHI;;;;;;;;;;;;;;;;+BAfR,KAAK,GAAG;;;;;wBAyBnB;;;;;;kCAGF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAEtD,8OAAC;gCAAK,UAAU;gCAAc,WAAU;;kDACtC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDACC,MAAK;wDACL,QAAQ;wDACR,OAAO,SAAS,IAAI;wDACpB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,MAAM,EAAE,MAAM,CAAC,KAAK;gEAAC,CAAC;wDACvE,WAAU;wDACV,aAAY;;;;;;;;;;;;0DAIhB,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDACC,MAAK;wDACL,QAAQ;wDACR,OAAO,SAAS,KAAK;wDACrB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gEAAC,CAAC;wDACxE,WAAU;wDACV,aAAY;;;;;;;;;;;;;;;;;;kDAKlB,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDAAI,WAAU;0DACZ,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,SAAS,IAAI,GAAG,OAAO,IAAI,CAAC,sBACrD,8OAAC;wDAEC,MAAK;wDACL,SAAS,IAAM,kBAAkB;wDACjC,WAAW,CAAC,iDAAiD,EAC3D,SAAS,MAAM,EAAE,SAAS,SACtB,qDACA,sEACJ;kEAED;uDATI;;;;;;;;;;;;;;;;kDAeb,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,OAAO,SAAS,UAAU;gDAC1B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,YAAY,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;gDAC7E,WAAU;gDACV,MAAM;gDACN,aAAY;;;;;;;;;;;;kDAIhB,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,QAAQ;gDACR,OAAO,SAAS,OAAO;gDACvB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,SAAS,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;gDAC1E,WAAU;gDACV,MAAM;gDACN,aAAY;;;;;;;;;;;;kDAIhB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,MAAK;gDACL,WAAU;0DACX;;;;;;0DAID,8OAAC;gDACC,MAAK;gDACL,QAAO;gDACP,KAAI;gDACJ,WAAU;;kEAEV,8OAAC;kEAAK;;;;;;kEACN,8OAAC,sNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQxC", "debugId": null}}]}