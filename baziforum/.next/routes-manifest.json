{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:file((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/]+\\.\\w+)/", "destination": "/:file", "internal": true, "missing": [{"type": "header", "key": "x-nextjs-data"}], "statusCode": 308, "regex": "^(?:/((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/]+\\.\\w+))/$"}, {"source": "/:notfile((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/\\.]+)", "destination": "/:notfile/", "internal": true, "statusCode": 308, "regex": "^(?:/((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/\\.]+))$"}], "headers": [], "dynamicRoutes": [{"page": "/post/[id]", "regex": "^/post/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/post/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/[slug]", "regex": "^/([^/]+?)(?:/)?$", "routeKeys": {"nxtPslug": "nxtPslug"}, "namedRegex": "^/(?<nxtPslug>[^/]+?)(?:/)?$"}, {"page": "/[slug]/new", "regex": "^/([^/]+?)/new(?:/)?$", "routeKeys": {"nxtPslug": "nxtPslug"}, "namedRegex": "^/(?<nxtPslug>[^/]+?)/new(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/about", "regex": "^/about(?:/)?$", "routeKeys": {}, "namedRegex": "^/about(?:/)?$"}, {"page": "/forum", "regex": "^/forum(?:/)?$", "routeKeys": {}, "namedRegex": "^/forum(?:/)?$"}, {"page": "/freemedia", "regex": "^/freemedia(?:/)?$", "routeKeys": {}, "namedRegex": "^/freemedia(?:/)?$"}, {"page": "/icon.svg", "regex": "^/icon\\.svg(?:/)?$", "routeKeys": {}, "namedRegex": "^/icon\\.svg(?:/)?$"}, {"page": "/join", "regex": "^/join(?:/)?$", "routeKeys": {}, "namedRegex": "^/join(?:/)?$"}, {"page": "/publicwatch", "regex": "^/publicwatch(?:/)?$", "routeKeys": {}, "namedRegex": "^/publicwatch(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}